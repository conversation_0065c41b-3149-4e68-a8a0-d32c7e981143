package com.example.sms_app.presentation;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = SmsApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface SmsApplication_GeneratedInjector {
  void injectSmsApplication(SmsApplication smsApplication);
}
