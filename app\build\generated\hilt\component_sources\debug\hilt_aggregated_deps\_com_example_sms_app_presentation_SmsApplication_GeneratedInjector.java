package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.components.SingletonComponent",
    entryPoints = "com.example.sms_app.presentation.SmsApplication_GeneratedInjector"
)
public class _com_example_sms_app_presentation_SmsApplication_GeneratedInjector {
}
