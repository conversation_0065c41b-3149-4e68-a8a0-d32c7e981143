  Manifest android  READ_PHONE_STATE android.Manifest.permission  READ_SMS android.Manifest.permission  RECEIVE_SMS android.Manifest.permission  SEND_SMS android.Manifest.permission  transparent android.R.color  SuppressLint android.annotation  Activity android.app  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  AutoSmsDisabler android.app.Activity  BackHandler android.app.Activity  Build android.app.Activity  Intent android.app.Activity  
MainScreen android.app.Activity  Manifest android.app.Activity  PermissionChecker android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  SendMessageViewModel android.app.Activity  Settings android.app.Activity  SmsAppTheme android.app.Activity  
SmsService android.app.Activity  Surface android.app.Activity  Toast android.app.Activity  android android.app.Activity  apply android.app.Activity  finish android.app.Activity  getValue android.app.Activity  
hiltViewModel android.app.Activity  initializeSafeMode android.app.Activity  java android.app.Activity  listOf android.app.Activity  mutableStateOf android.app.Activity  observeAsState android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  remember android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  window android.app.Activity  Boolean android.app.Application  	Exception android.app.Application  File android.app.Application  Int android.app.Application  Log android.app.Application  String android.app.Application  	Throwable android.app.Application  Timber android.app.Application  
appendText android.app.Application  applicationContext android.app.Application  i android.app.Application  let android.app.Application  onCreate android.app.Application  plant android.app.Application  registerReceiver android.app.Application  stopService android.app.Application  unregisterReceiver android.app.Application  	DebugTree android.app.Application.Timber  VISIBILITY_PRIVATE android.app.Notification  Notification android.app.NotificationChannel  android android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  
lightColor android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  longArrayOf android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  vibrationPattern android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  getBroadcast android.app.PendingIntent  ACTION_CUSTOMER_DELETED android.app.Service  ACTION_PROGRESS_UPDATE android.app.Service  ACTION_SMS_COMPLETED android.app.Service  ACTION_SMS_COUNT_UPDATED android.app.Service  Activity android.app.Service  	ArrayList android.app.Service  Boolean android.app.Service  BroadcastReceiver android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  CancellationException android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Customer android.app.Service  Dispatchers android.app.Service  ERROR_RETRY_FAILED android.app.Service  EXTRA_CUSTOMER_ID android.app.Service  EXTRA_INTERVAL_SECONDS android.app.Service  EXTRA_MAX_RETRY android.app.Service  
EXTRA_MESSAGE android.app.Service  EXTRA_PROGRESS android.app.Service  EXTRA_RETRY_DELAY android.app.Service  EXTRA_SIM_ID android.app.Service  EXTRA_SMS_COUNT android.app.Service  EXTRA_TEMPLATE_ID android.app.Service  EXTRA_TOTAL android.app.Service  	Exception android.app.Service  Handler android.app.Service  IllegalStateException android.app.Service  Intent android.app.Service  IntentFilter android.app.Service  Log android.app.Service  Looper android.app.Service  Manifest android.app.Service  MutableList android.app.Service  
MutableSet android.app.Service  NOTIFICATION_CHANNEL_ID android.app.Service  NOTIFICATION_ID android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  Pair android.app.Service  
PendingIntent android.app.Service  Regex android.app.Service  SMS_DELIVERED_ACTION android.app.Service  SMS_SENT_ACTION android.app.Service  START_NOT_STICKY android.app.Service  START_REDELIVER_INTENT android.app.Service  SecurityException android.app.Service  Settings android.app.Service  
SmsAttempt android.app.Service  
SmsManager android.app.Service  
SmsRepository android.app.Service  SmsTemplate android.app.Service  String android.app.Service  SubscriptionManager android.app.Service  System android.app.Service  TAG android.app.Service  TELEPHONY_SERVICE android.app.Service  TelephonyManager android.app.Service  activeAttempts android.app.Service  android android.app.Service  any android.app.Service  apply android.app.Service  arrayOf android.app.Service  async android.app.Service  attemptQueue android.app.Service  coerceIn android.app.Service  com android.app.Service  contains android.app.Service  coroutineScope android.app.Service  delay android.app.Service  distinct android.app.Service  endsWith android.app.Service  executeNextAttempt android.app.Service  filter android.app.Service  find android.app.Service  format android.app.Service  getRandomDelay android.app.Service  getSystemService android.app.Service  handleSendFailure android.app.Service  hasRequiredPermissions android.app.Service  indices android.app.Service  
initialize android.app.Service  
isNotEmpty android.app.Service  
isNullOrEmpty android.app.Service  	isRunning android.app.Service  java android.app.Service  joinToString android.app.Service  kotlinx android.app.Service  launch android.app.Service  let android.app.Service  listOf android.app.Service  longArrayOf android.app.Service  map android.app.Service  maxOf android.app.Service  minOf android.app.Service  minusAssign android.app.Service  
mutableListOf android.app.Service  mutableMapOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  pendingSmsDeliveries android.app.Service  pendingSmsResults android.app.Service  
plusAssign android.app.Service  random android.app.Service  replace android.app.Service  resume android.app.Service  sendCompletionBroadcast android.app.Service  sendProgressBroadcast android.app.Service  sendSmsToCustomers android.app.Service  sendSmsWithDeliveryReport android.app.Service  set android.app.Service  startForeground android.app.Service  startSendingSms android.app.Service  
startsWith android.app.Service  stopSelf android.app.Service  	substring android.app.Service  suspendCancellableCoroutine android.app.Service  take android.app.Service  to android.app.Service  	totalSent android.app.Service  totalToSend android.app.Service  trim android.app.Service  	withIndex android.app.Service  
coroutines android.app.Service.kotlinx  CancellableContinuation &android.app.Service.kotlinx.coroutines  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  IntentFilter android.content  SharedPreferences android.content  Activity !android.content.BroadcastReceiver  Build !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  CoroutineScope !android.content.BroadcastReceiver  Dispatchers !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  SMS_DELIVERED_ACTION !android.content.BroadcastReceiver  SMS_SENT_ACTION !android.content.BroadcastReceiver  
SessionBackup !android.content.BroadcastReceiver  
SmsManager !android.content.BroadcastReceiver  SmsProgress !android.content.BroadcastReceiver  
SmsRepository !android.content.BroadcastReceiver  
SmsService !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  TelephonyManager !android.content.BroadcastReceiver  Timber !android.content.BroadcastReceiver  _completion !android.content.BroadcastReceiver  
_isSending !android.content.BroadcastReceiver  _millisUntilFinished !android.content.BroadcastReceiver  	_progress !android.content.BroadcastReceiver  activeAttempts !android.content.BroadcastReceiver  android !android.content.BroadcastReceiver  attemptQueue !android.content.BroadcastReceiver  currentCountDownTimer !android.content.BroadcastReceiver  delay !android.content.BroadcastReceiver  executeNextAttempt !android.content.BroadcastReceiver  getApplication !android.content.BroadcastReceiver  getSystemService !android.content.BroadcastReceiver  
isNotEmpty !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  launch !android.content.BroadcastReceiver  let !android.content.BroadcastReceiver  pendingSmsDeliveries !android.content.BroadcastReceiver  pendingSmsResults !android.content.BroadcastReceiver  
resultCode !android.content.BroadcastReceiver  resume !android.content.BroadcastReceiver  sendProgressBroadcast !android.content.BroadcastReceiver  
smsRepository !android.content.BroadcastReceiver  sync !android.content.BroadcastReceiver  tag !android.content.BroadcastReceiver  	totalSent !android.content.BroadcastReceiver  totalToSend !android.content.BroadcastReceiver  viewModelScope !android.content.BroadcastReceiver  openInputStream android.content.ContentResolver  ACTION_CUSTOMER_DELETED android.content.Context  ACTION_PROGRESS_UPDATE android.content.Context  ACTION_SMS_COMPLETED android.content.Context  ACTION_SMS_COUNT_UPDATED android.content.Context  Activity android.content.Context  	ArrayList android.content.Context  AutoSmsDisabler android.content.Context  BackHandler android.content.Context  Boolean android.content.Context  BroadcastReceiver android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  CancellationException android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  Customer android.content.Context  Dispatchers android.content.Context  ERROR_RETRY_FAILED android.content.Context  EXTRA_CUSTOMER_ID android.content.Context  EXTRA_INTERVAL_SECONDS android.content.Context  EXTRA_MAX_RETRY android.content.Context  
EXTRA_MESSAGE android.content.Context  EXTRA_PROGRESS android.content.Context  EXTRA_RETRY_DELAY android.content.Context  EXTRA_SIM_ID android.content.Context  EXTRA_SMS_COUNT android.content.Context  EXTRA_TEMPLATE_ID android.content.Context  EXTRA_TOTAL android.content.Context  	Exception android.content.Context  File android.content.Context  Handler android.content.Context  IllegalStateException android.content.Context  Int android.content.Context  Intent android.content.Context  IntentFilter android.content.Context  Log android.content.Context  Looper android.content.Context  MODE_PRIVATE android.content.Context  
MainScreen android.content.Context  Manifest android.content.Context  MutableList android.content.Context  
MutableSet android.content.Context  NOTIFICATION_CHANNEL_ID android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  Pair android.content.Context  
PendingIntent android.content.Context  PermissionChecker android.content.Context  RECEIVER_NOT_EXPORTED android.content.Context  Regex android.content.Context  SMS_DELIVERED_ACTION android.content.Context  SMS_SENT_ACTION android.content.Context  START_NOT_STICKY android.content.Context  START_REDELIVER_INTENT android.content.Context  SecurityException android.content.Context  SendMessageViewModel android.content.Context  Settings android.content.Context  SmsAppTheme android.content.Context  
SmsAttempt android.content.Context  
SmsManager android.content.Context  
SmsRepository android.content.Context  
SmsService android.content.Context  SmsTemplate android.content.Context  String android.content.Context  SubscriptionManager android.content.Context  Surface android.content.Context  System android.content.Context  TAG android.content.Context  TELEPHONY_SERVICE android.content.Context  TELEPHONY_SUBSCRIPTION_SERVICE android.content.Context  TelephonyManager android.content.Context  	Throwable android.content.Context  Timber android.content.Context  Toast android.content.Context  activeAttempts android.content.Context  android android.content.Context  any android.content.Context  
appendText android.content.Context  applicationContext android.content.Context  apply android.content.Context  arrayOf android.content.Context  assets android.content.Context  async android.content.Context  attemptQueue android.content.Context  cacheDir android.content.Context  classLoader android.content.Context  coerceIn android.content.Context  com android.content.Context  contains android.content.Context  contentResolver android.content.Context  coroutineScope android.content.Context  delay android.content.Context  distinct android.content.Context  endsWith android.content.Context  executeNextAttempt android.content.Context  filter android.content.Context  find android.content.Context  format android.content.Context  getExternalFilesDir android.content.Context  getOrDefault android.content.Context  getRandomDelay android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  getValue android.content.Context  handleSendFailure android.content.Context  hasRequiredPermissions android.content.Context  
hiltViewModel android.content.Context  i android.content.Context  indices android.content.Context  
initialize android.content.Context  initializeSafeMode android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  	isRunning android.content.Context  java android.content.Context  joinToString android.content.Context  kotlinx android.content.Context  launch android.content.Context  let android.content.Context  listOf android.content.Context  longArrayOf android.content.Context  map android.content.Context  maxOf android.content.Context  minOf android.content.Context  minusAssign android.content.Context  
mutableListOf android.content.Context  mutableMapOf android.content.Context  mutableStateOf android.content.Context  observeAsState android.content.Context  packageManager android.content.Context  packageName android.content.Context  pendingSmsDeliveries android.content.Context  pendingSmsResults android.content.Context  plant android.content.Context  
plusAssign android.content.Context  provideDelegate android.content.Context  random android.content.Context  remember android.content.Context  replace android.content.Context  resume android.content.Context  runCatching android.content.Context  
sendBroadcast android.content.Context  sendCompletionBroadcast android.content.Context  sendProgressBroadcast android.content.Context  sendSmsToCustomers android.content.Context  sendSmsWithDeliveryReport android.content.Context  set android.content.Context  
setContent android.content.Context  setValue android.content.Context  
startActivity android.content.Context  startForegroundService android.content.Context  startSendingSms android.content.Context  startService android.content.Context  
startsWith android.content.Context  stopSelf android.content.Context  	substring android.content.Context  suspendCancellableCoroutine android.content.Context  take android.content.Context  to android.content.Context  	totalSent android.content.Context  totalToSend android.content.Context  trim android.content.Context  	withIndex android.content.Context  	DebugTree android.content.Context.Timber  
coroutines android.content.Context.kotlinx  CancellableContinuation *android.content.Context.kotlinx.coroutines  ACTION_CUSTOMER_DELETED android.content.ContextWrapper  ACTION_PROGRESS_UPDATE android.content.ContextWrapper  ACTION_SMS_COMPLETED android.content.ContextWrapper  ACTION_SMS_COUNT_UPDATED android.content.ContextWrapper  Activity android.content.ContextWrapper  	ArrayList android.content.ContextWrapper  AutoSmsDisabler android.content.ContextWrapper  BackHandler android.content.ContextWrapper  Boolean android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CancellationException android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Customer android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  ERROR_RETRY_FAILED android.content.ContextWrapper  EXTRA_CUSTOMER_ID android.content.ContextWrapper  EXTRA_INTERVAL_SECONDS android.content.ContextWrapper  EXTRA_MAX_RETRY android.content.ContextWrapper  
EXTRA_MESSAGE android.content.ContextWrapper  EXTRA_PROGRESS android.content.ContextWrapper  EXTRA_RETRY_DELAY android.content.ContextWrapper  EXTRA_SIM_ID android.content.ContextWrapper  EXTRA_SMS_COUNT android.content.ContextWrapper  EXTRA_TEMPLATE_ID android.content.ContextWrapper  EXTRA_TOTAL android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  Handler android.content.ContextWrapper  IllegalStateException android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  Manifest android.content.ContextWrapper  MutableList android.content.ContextWrapper  
MutableSet android.content.ContextWrapper  NOTIFICATION_CHANNEL_ID android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  Pair android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PermissionChecker android.content.ContextWrapper  Regex android.content.ContextWrapper  SMS_DELIVERED_ACTION android.content.ContextWrapper  SMS_SENT_ACTION android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_REDELIVER_INTENT android.content.ContextWrapper  SecurityException android.content.ContextWrapper  SendMessageViewModel android.content.ContextWrapper  Settings android.content.ContextWrapper  SmsAppTheme android.content.ContextWrapper  
SmsAttempt android.content.ContextWrapper  
SmsManager android.content.ContextWrapper  
SmsRepository android.content.ContextWrapper  
SmsService android.content.ContextWrapper  SmsTemplate android.content.ContextWrapper  String android.content.ContextWrapper  SubscriptionManager android.content.ContextWrapper  Surface android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  TELEPHONY_SERVICE android.content.ContextWrapper  TelephonyManager android.content.ContextWrapper  	Throwable android.content.ContextWrapper  Timber android.content.ContextWrapper  Toast android.content.ContextWrapper  activeAttempts android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  
appendText android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  arrayOf android.content.ContextWrapper  async android.content.ContextWrapper  attemptQueue android.content.ContextWrapper  coerceIn android.content.ContextWrapper  com android.content.ContextWrapper  contains android.content.ContextWrapper  contentResolver android.content.ContextWrapper  coroutineScope android.content.ContextWrapper  delay android.content.ContextWrapper  distinct android.content.ContextWrapper  endsWith android.content.ContextWrapper  executeNextAttempt android.content.ContextWrapper  filter android.content.ContextWrapper  find android.content.ContextWrapper  format android.content.ContextWrapper  getRandomDelay android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getValue android.content.ContextWrapper  handleSendFailure android.content.ContextWrapper  hasRequiredPermissions android.content.ContextWrapper  
hiltViewModel android.content.ContextWrapper  i android.content.ContextWrapper  indices android.content.ContextWrapper  
initialize android.content.ContextWrapper  initializeSafeMode android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  	isRunning android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  kotlinx android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  longArrayOf android.content.ContextWrapper  map android.content.ContextWrapper  maxOf android.content.ContextWrapper  minOf android.content.ContextWrapper  minusAssign android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  observeAsState android.content.ContextWrapper  pendingSmsDeliveries android.content.ContextWrapper  pendingSmsResults android.content.ContextWrapper  plant android.content.ContextWrapper  
plusAssign android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  random android.content.ContextWrapper  registerReceiver android.content.ContextWrapper  remember android.content.ContextWrapper  replace android.content.ContextWrapper  resume android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  sendCompletionBroadcast android.content.ContextWrapper  sendProgressBroadcast android.content.ContextWrapper  sendSmsToCustomers android.content.ContextWrapper  sendSmsWithDeliveryReport android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  startSendingSms android.content.ContextWrapper  
startsWith android.content.ContextWrapper  stopSelf android.content.ContextWrapper  stopService android.content.ContextWrapper  	substring android.content.ContextWrapper  suspendCancellableCoroutine android.content.ContextWrapper  take android.content.ContextWrapper  to android.content.ContextWrapper  	totalSent android.content.ContextWrapper  totalToSend android.content.ContextWrapper  trim android.content.ContextWrapper  unregisterReceiver android.content.ContextWrapper  	withIndex android.content.ContextWrapper  	DebugTree %android.content.ContextWrapper.Timber  
coroutines &android.content.ContextWrapper.kotlinx  CancellableContinuation 1android.content.ContextWrapper.kotlinx.coroutines  
ACTION_DELETE android.content.Intent  ACTION_DIAL android.content.Intent  
ACTION_SENDTO android.content.Intent  ACTION_VIEW android.content.Intent  EXTRA_CUSTOMER_ID android.content.Intent  
EXTRA_MESSAGE android.content.Intent  EXTRA_PROGRESS android.content.Intent  EXTRA_SIM_ID android.content.Intent  EXTRA_SMS_COUNT android.content.Intent  
EXTRA_SUBJECT android.content.Intent  EXTRA_TOTAL android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  FLAG_INCLUDE_STOPPED_PACKAGES android.content.Intent  FLAG_RECEIVER_FOREGROUND android.content.Intent  Intent android.content.Intent  
SmsService android.content.Intent  Uri android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  context android.content.Intent  
createChooser android.content.Intent  data android.content.Intent  flags android.content.Intent  getIntExtra android.content.Intent  getStringExtra android.content.Intent  let android.content.Intent  putExtra android.content.Intent  resolveActivity android.content.Intent  	setAction android.content.Intent  setDataAndType android.content.Intent  
setPackage android.content.Intent  SMS_DELIVERED_ACTION android.content.IntentFilter  SMS_SENT_ACTION android.content.IntentFilter  
SmsService android.content.IntentFilter  	addAction android.content.IntentFilter  apply android.content.IntentFilter  let android.content.IntentFilter  Editor !android.content.SharedPreferences  all !android.content.SharedPreferences  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  KEY_COUNTDOWN_CUSTOMER_COUNT (android.content.SharedPreferences.Editor  KEY_COUNTDOWN_START_TIME (android.content.SharedPreferences.Editor  KEY_COUNTDOWN_TOTAL_TIME (android.content.SharedPreferences.Editor  apply (android.content.SharedPreferences.Editor  commit (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  PackageInfo android.content.pm  PackageManager android.content.pm  	Signature android.content.pm  longVersionCode android.content.pm.PackageInfo  packageName android.content.pm.PackageInfo  
signatures android.content.pm.PackageInfo  signingInfo android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  GET_ACTIVITIES !android.content.pm.PackageManager  GET_SIGNATURES !android.content.pm.PackageManager  GET_SIGNING_CERTIFICATES !android.content.pm.PackageManager  NameNotFoundException !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  canRequestPackageInstalls !android.content.pm.PackageManager  checkPermission !android.content.pm.PackageManager  getPackageArchiveInfo !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  toByteArray android.content.pm.Signature  apkContentsSigners android.content.pm.SigningInfo  open  android.content.res.AssetManager  BLUE android.graphics.Color  Uri android.net  fromFile android.net.Uri  let android.net.Uri  parse android.net.Uri  Build 
android.os  Bundle 
android.os  CountDownTimer 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  BRAND android.os.Build  DEVICE android.os.Build  FINGERPRINT android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  SUPPORTED_ABIS android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  Dispatchers android.os.CountDownTimer  	Exception android.os.CountDownTimer  Log android.os.CountDownTimer  TAG android.os.CountDownTimer  
_isSending android.os.CountDownTimer  _millisUntilFinished android.os.CountDownTimer  android android.os.CountDownTimer  cancel android.os.CountDownTimer  launch android.os.CountDownTimer  
smsRepository android.os.CountDownTimer  start android.os.CountDownTimer  viewModelScope android.os.CountDownTimer  DIRECTORY_DOWNLOADS android.os.Environment  getExternalStorageDirectory android.os.Environment  isExternalStorageManager android.os.Environment  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  )ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION android.provider.Settings  !ACTION_MANAGE_UNKNOWN_APP_SOURCES android.provider.Settings  ADB_ENABLED  android.provider.Settings.Global  AIRPLANE_MODE_ON  android.provider.Settings.Global  getInt  android.provider.Settings.Global  getInt  android.provider.Settings.Secure  KeyGenParameterSpec android.security.keystore  
KeyProperties android.security.keystore  Builder -android.security.keystore.KeyGenParameterSpec  build 5android.security.keystore.KeyGenParameterSpec.Builder  
setBlockModes 5android.security.keystore.KeyGenParameterSpec.Builder  setEncryptionPaddings 5android.security.keystore.KeyGenParameterSpec.Builder  setRandomizedEncryptionRequired 5android.security.keystore.KeyGenParameterSpec.Builder  BLOCK_MODE_GCM 'android.security.keystore.KeyProperties  ENCRYPTION_PADDING_NONE 'android.security.keystore.KeyProperties  KEY_ALGORITHM_AES 'android.security.keystore.KeyProperties  PURPOSE_DECRYPT 'android.security.keystore.KeyProperties  PURPOSE_ENCRYPT 'android.security.keystore.KeyProperties  
SmsManager android.telephony  SubscriptionInfo android.telephony  SubscriptionManager android.telephony  TelephonyManager android.telephony  RESULT_ERROR_GENERIC_FAILURE android.telephony.SmsManager  RESULT_ERROR_NO_SERVICE android.telephony.SmsManager  RESULT_ERROR_NULL_PDU android.telephony.SmsManager  RESULT_ERROR_RADIO_OFF android.telephony.SmsManager  
divideMessage android.telephony.SmsManager  
getDefault android.telephony.SmsManager  getSmsManagerForSubscriptionId android.telephony.SmsManager  sendMultipartTextMessage android.telephony.SmsManager  sendTextMessage android.telephony.SmsManager  carrierName "android.telephony.SubscriptionInfo  displayName "android.telephony.SubscriptionInfo  number "android.telephony.SubscriptionInfo  simSlotIndex "android.telephony.SubscriptionInfo  subscriptionId "android.telephony.SubscriptionInfo  activeSubscriptionInfoList %android.telephony.SubscriptionManager  getActiveSubscriptionInfo %android.telephony.SubscriptionManager  NETWORK_TYPE_UNKNOWN "android.telephony.TelephonyManager  PHONE_TYPE_NONE "android.telephony.TelephonyManager  SIM_STATE_ABSENT "android.telephony.TelephonyManager  SIM_STATE_NETWORK_LOCKED "android.telephony.TelephonyManager  SIM_STATE_PIN_REQUIRED "android.telephony.TelephonyManager  SIM_STATE_PUK_REQUIRED "android.telephony.TelephonyManager  SIM_STATE_READY "android.telephony.TelephonyManager  SIM_STATE_UNKNOWN "android.telephony.TelephonyManager  networkOperatorName "android.telephony.TelephonyManager  networkType "android.telephony.TelephonyManager  	phoneType "android.telephony.TelephonyManager  simState "android.telephony.TelephonyManager  Base64 android.util  Log android.util  DEFAULT android.util.Base64  NO_WRAP android.util.Base64  decode android.util.Base64  encodeToString android.util.Base64  ASSERT android.util.Log  DEBUG android.util.Log  ERROR android.util.Log  INFO android.util.Log  VERBOSE android.util.Log  WARN android.util.Log  d android.util.Log  e android.util.Log  getStackTraceString android.util.Log  i android.util.Log  w android.util.Log  AutoSmsDisabler  android.view.ContextThemeWrapper  BackHandler  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PermissionChecker  android.view.ContextThemeWrapper  SendMessageViewModel  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  SmsAppTheme  android.view.ContextThemeWrapper  
SmsService  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  
hiltViewModel  android.view.ContextThemeWrapper  initializeSafeMode  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  observeAsState  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  remember  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  addFlags android.view.Window  
clearFlags android.view.Window  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  AutoSmsDisabler #androidx.activity.ComponentActivity  BackHandler #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PermissionChecker #androidx.activity.ComponentActivity  SendMessageViewModel #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  SmsAppTheme #androidx.activity.ComponentActivity  
SmsService #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  
hiltViewModel #androidx.activity.ComponentActivity  initializeSafeMode #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  observeAsState #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  remember #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  BackHandler androidx.activity.compose  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  
resultCode 'androidx.activity.result.ActivityResult  ActivityResultContracts !androidx.activity.result.contract  OpenDocument 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  Keep androidx.annotation  RequiresPermission androidx.annotation  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  combinedClickable androidx.compose.foundation  horizontalScroll androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectTapGestures $androidx.compose.foundation.gestures  AboutScreen "androidx.compose.foundation.layout  
AccessTime "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AppSettings "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Customer "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  Environment "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  File "androidx.compose.foundation.layout  FileItem "androidx.compose.foundation.layout  FileItemRow "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  HorizontalDivider "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  MessageTemplate "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RadioButtonDefaults "androidx.compose.foundation.layout  Refresh "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Save "androidx.compose.foundation.layout  Schedule "androidx.compose.foundation.layout  SecurityException "androidx.compose.foundation.layout  SettingItem "androidx.compose.foundation.layout  SettingSection "androidx.compose.foundation.layout  SimCard "androidx.compose.foundation.layout  
SimManager "androidx.compose.foundation.layout  
SmsRepository "androidx.compose.foundation.layout  
SmsSession "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  TemplateManager "androidx.compose.foundation.layout  TemplateSelectionItem "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Timer "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  any "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  	compareBy "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  endsWith "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getAvailableSims "androidx.compose.foundation.layout  getDefaultTemplates "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  	lowercase "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  
mapNotNull "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  painterResource "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  replace "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
sortedWith "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  
startsWith "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  textButtonColors "androidx.compose.foundation.layout  thenBy "androidx.compose.foundation.layout  toInt "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  widthIn "androidx.compose.foundation.layout  wrapContentHeight "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Close +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
TextButton +androidx.compose.foundation.layout.BoxScope  	TextField +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  format +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  textButtonColors +androidx.compose.foundation.layout.BoxScope  AboutScreen .androidx.compose.foundation.layout.ColumnScope  
AccessTime .androidx.compose.foundation.layout.ColumnScope  
AccountBox .androidx.compose.foundation.layout.ColumnScope  AlertDialog .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AppSettings .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  BottomAppBar .androidx.compose.foundation.layout.ColumnScope  BottomButton .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CheckCircle .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CheckboxDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  CoroutineScope .androidx.compose.foundation.layout.ColumnScope  Customer .androidx.compose.foundation.layout.ColumnScope  
CustomerField .androidx.compose.foundation.layout.ColumnScope  Date .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Dispatchers .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Environment .androidx.compose.foundation.layout.ColumnScope  Error .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  File .androidx.compose.foundation.layout.ColumnScope  FileItemRow .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Home .androidx.compose.foundation.layout.ColumnScope  HorizontalDivider .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ListItem .androidx.compose.foundation.layout.ColumnScope  Locale .androidx.compose.foundation.layout.ColumnScope  Log .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  MenuAnchorType .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MoreVertFunctions .androidx.compose.foundation.layout.ColumnScope  MoreView .androidx.compose.foundation.layout.ColumnScope  
NumSetting .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  Phone .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RadioButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Save .androidx.compose.foundation.layout.ColumnScope  Scaffold .androidx.compose.foundation.layout.ColumnScope  Schedule .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  Send .androidx.compose.foundation.layout.ColumnScope  SettingItem .androidx.compose.foundation.layout.ColumnScope  SettingSection .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  SimCard .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  
SwitchSetting .androidx.compose.foundation.layout.ColumnScope  TODO .androidx.compose.foundation.layout.ColumnScope  TemplateSelectionItem .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  	TextField .androidx.compose.foundation.layout.ColumnScope  TextSetting .androidx.compose.foundation.layout.ColumnScope  	TextStyle .androidx.compose.foundation.layout.ColumnScope  Timer .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  	TopAppBar .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  UUID .androidx.compose.foundation.layout.ColumnScope  Unit .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  all .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  any .androidx.compose.foundation.layout.ColumnScope  
associateWith .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
capitalize .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope  contains .androidx.compose.foundation.layout.ColumnScope  count .androidx.compose.foundation.layout.ColumnScope  detectTapGestures .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  endsWith .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  filter .androidx.compose.foundation.layout.ColumnScope  find .androidx.compose.foundation.layout.ColumnScope  first .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  getAvailableSims .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  horizontalScroll .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  isValidPhoneNumber .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  	lowercase .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  
menuAnchor .androidx.compose.foundation.layout.ColumnScope  minus .androidx.compose.foundation.layout.ColumnScope  mutableStateOf .androidx.compose.foundation.layout.ColumnScope  offset .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  pointerInput .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  remember .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  replace .androidx.compose.foundation.layout.ColumnScope  setValue .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  takeIf .androidx.compose.foundation.layout.ColumnScope  textButtonColors .androidx.compose.foundation.layout.ColumnScope  textFieldColors .androidx.compose.foundation.layout.ColumnScope  toInt .androidx.compose.foundation.layout.ColumnScope  toIntOrNull .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  validateAndFormatPhoneNumber .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  BorderStroke +androidx.compose.foundation.layout.RowScope  BottomButton +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CheckCircle +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CheckboxDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CoroutineScope +androidx.compose.foundation.layout.RowScope  
CustomerField +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Dispatchers +androidx.compose.foundation.layout.RowScope  Download +androidx.compose.foundation.layout.RowScope  Environment +androidx.compose.foundation.layout.RowScope  Error +androidx.compose.foundation.layout.RowScope  File +androidx.compose.foundation.layout.RowScope  Folder +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  HorizontalDivider +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  InsertDriveFile +androidx.compose.foundation.layout.RowScope  Locale +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreView +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  
PaddingValues +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RadioButtonDefaults +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Save +androidx.compose.foundation.layout.RowScope  Send +androidx.compose.foundation.layout.RowScope  SimCard +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  	TextStyle +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  Warning +androidx.compose.foundation.layout.RowScope  all +androidx.compose.foundation.layout.RowScope  android +androidx.compose.foundation.layout.RowScope  any +androidx.compose.foundation.layout.RowScope  
associateWith +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  
capitalize +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  contains +androidx.compose.foundation.layout.RowScope  count +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  horizontalScroll +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  	lowercase +androidx.compose.foundation.layout.RowScope  map +androidx.compose.foundation.layout.RowScope  mutableStateOf +androidx.compose.foundation.layout.RowScope  outlinedButtonColors +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  remember +androidx.compose.foundation.layout.RowScope  rememberScrollState +androidx.compose.foundation.layout.RowScope  setValue +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  take +androidx.compose.foundation.layout.RowScope  	uppercase +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  AlertDialog .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  Checkbox .androidx.compose.foundation.lazy.LazyItemScope  CheckboxDefaults .androidx.compose.foundation.lazy.LazyItemScope  Close .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  
CustomerField .androidx.compose.foundation.lazy.LazyItemScope  Date .androidx.compose.foundation.lazy.LazyItemScope  Delete .androidx.compose.foundation.lazy.LazyItemScope  Divider .androidx.compose.foundation.lazy.LazyItemScope  Edit .androidx.compose.foundation.lazy.LazyItemScope  FileItemRow .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  HorizontalDivider .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  
IconButton .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyItemScope  KeyboardType .androidx.compose.foundation.lazy.LazyItemScope  ListItem .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  
NumSetting .androidx.compose.foundation.lazy.LazyItemScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyItemScope  
PaddingValues .androidx.compose.foundation.lazy.LazyItemScope  Person .androidx.compose.foundation.lazy.LazyItemScope  RadioButton .androidx.compose.foundation.lazy.LazyItemScope  RadioButtonDefaults .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  SimCard .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Switch .androidx.compose.foundation.lazy.LazyItemScope  
SwitchSetting .androidx.compose.foundation.lazy.LazyItemScope  TemplateSelectionItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  	TextAlign .androidx.compose.foundation.lazy.LazyItemScope  
TextButton .androidx.compose.foundation.lazy.LazyItemScope  	TextField .androidx.compose.foundation.lazy.LazyItemScope  TextSetting .androidx.compose.foundation.lazy.LazyItemScope  	TextStyle .androidx.compose.foundation.lazy.LazyItemScope  all .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyItemScope  androidx .androidx.compose.foundation.lazy.LazyItemScope  buttonColors .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  colors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  endsWith .androidx.compose.foundation.lazy.LazyItemScope  fillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  find .androidx.compose.foundation.lazy.LazyItemScope  getValue .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  let .androidx.compose.foundation.lazy.LazyItemScope  map .androidx.compose.foundation.lazy.LazyItemScope  minus .androidx.compose.foundation.lazy.LazyItemScope  mutableStateOf .androidx.compose.foundation.lazy.LazyItemScope  offset .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  plus .androidx.compose.foundation.lazy.LazyItemScope  provideDelegate .androidx.compose.foundation.lazy.LazyItemScope  remember .androidx.compose.foundation.lazy.LazyItemScope  setValue .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  take .androidx.compose.foundation.lazy.LazyItemScope  takeIf .androidx.compose.foundation.lazy.LazyItemScope  toIntOrNull .androidx.compose.foundation.lazy.LazyItemScope  	uppercase .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  AlertDialog .androidx.compose.foundation.lazy.LazyListScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  Checkbox .androidx.compose.foundation.lazy.LazyListScope  CheckboxDefaults .androidx.compose.foundation.lazy.LazyListScope  Close .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  
CustomerField .androidx.compose.foundation.lazy.LazyListScope  Date .androidx.compose.foundation.lazy.LazyListScope  Delete .androidx.compose.foundation.lazy.LazyListScope  Divider .androidx.compose.foundation.lazy.LazyListScope  Edit .androidx.compose.foundation.lazy.LazyListScope  FileItemRow .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  HorizontalDivider .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  
IconButton .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyListScope  KeyboardType .androidx.compose.foundation.lazy.LazyListScope  ListItem .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  
NumSetting .androidx.compose.foundation.lazy.LazyListScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyListScope  
PaddingValues .androidx.compose.foundation.lazy.LazyListScope  Person .androidx.compose.foundation.lazy.LazyListScope  RadioButton .androidx.compose.foundation.lazy.LazyListScope  RadioButtonDefaults .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  SimCard .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Switch .androidx.compose.foundation.lazy.LazyListScope  
SwitchSetting .androidx.compose.foundation.lazy.LazyListScope  TemplateSelectionItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  	TextAlign .androidx.compose.foundation.lazy.LazyListScope  
TextButton .androidx.compose.foundation.lazy.LazyListScope  	TextField .androidx.compose.foundation.lazy.LazyListScope  TextSetting .androidx.compose.foundation.lazy.LazyListScope  	TextStyle .androidx.compose.foundation.lazy.LazyListScope  all .androidx.compose.foundation.lazy.LazyListScope  android .androidx.compose.foundation.lazy.LazyListScope  androidx .androidx.compose.foundation.lazy.LazyListScope  buttonColors .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  colors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  endsWith .androidx.compose.foundation.lazy.LazyListScope  fillMaxSize .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  filter .androidx.compose.foundation.lazy.LazyListScope  find .androidx.compose.foundation.lazy.LazyListScope  getValue .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  let .androidx.compose.foundation.lazy.LazyListScope  map .androidx.compose.foundation.lazy.LazyListScope  minus .androidx.compose.foundation.lazy.LazyListScope  mutableStateOf .androidx.compose.foundation.lazy.LazyListScope  offset .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  plus .androidx.compose.foundation.lazy.LazyListScope  provideDelegate .androidx.compose.foundation.lazy.LazyListScope  remember .androidx.compose.foundation.lazy.LazyListScope  setValue .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  take .androidx.compose.foundation.lazy.LazyListScope  takeIf .androidx.compose.foundation.lazy.LazyListScope  toIntOrNull .androidx.compose.foundation.lazy.LazyListScope  	uppercase .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ExitToApp 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Message 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Notes 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Send 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  
AccessTime ,androidx.compose.material.icons.Icons.Filled  
AccountBox ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Call ,androidx.compose.material.icons.Icons.Filled  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  
CloudDownload ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  DocumentScanner ,androidx.compose.material.icons.Icons.Filled  Download ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Error ,androidx.compose.material.icons.Icons.Filled  	FilterAlt ,androidx.compose.material.icons.Icons.Filled  Folder ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  InsertDriveFile ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PersonAdd ,androidx.compose.material.icons.Icons.Filled  Phone ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Save ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Send ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  SettingsBackupRestore ,androidx.compose.material.icons.Icons.Filled  SimCard ,androidx.compose.material.icons.Icons.Filled  Timer ,androidx.compose.material.icons.Icons.Filled  Update ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  	ExitToApp 3androidx.compose.material.icons.automirrored.filled  Message 3androidx.compose.material.icons.automirrored.filled  Notes 3androidx.compose.material.icons.automirrored.filled  Send 3androidx.compose.material.icons.automirrored.filled  AboutScreen &androidx.compose.material.icons.filled  
AccessTime &androidx.compose.material.icons.filled  
AccountBox &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AppSettings &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Cake &androidx.compose.material.icons.filled  Call &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  ChangeCircle &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  
CloudDownload &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DeleteForever &androidx.compose.material.icons.filled  Divider &androidx.compose.material.icons.filled  DocumentScanner &androidx.compose.material.icons.filled  Download &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Error &androidx.compose.material.icons.filled  
FileUpload &androidx.compose.material.icons.filled  	FilterAlt &androidx.compose.material.icons.filled  Folder &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  InsertDriveFile &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  OutlinedTextFieldDefaults &androidx.compose.material.icons.filled  
PaddingValues &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PersonAdd &androidx.compose.material.icons.filled  Phone &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Save &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Send &androidx.compose.material.icons.filled  SettingItem &androidx.compose.material.icons.filled  SettingSection &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsBackupRestore &androidx.compose.material.icons.filled  SimCard &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  Timer &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Update &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  
cardElevation &androidx.compose.material.icons.filled  colors &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  contains &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  getAvailableSims &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  isEmpty &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberScrollState &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  toInt &androidx.compose.material.icons.filled  toIntOrNull &androidx.compose.material.icons.filled  verticalScroll &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  AboutScreen (androidx.compose.material.icons.outlined  
AccessTime (androidx.compose.material.icons.outlined  	Alignment (androidx.compose.material.icons.outlined  AppSettings (androidx.compose.material.icons.outlined  Arrangement (androidx.compose.material.icons.outlined  Box (androidx.compose.material.icons.outlined  Button (androidx.compose.material.icons.outlined  ButtonDefaults (androidx.compose.material.icons.outlined  Card (androidx.compose.material.icons.outlined  CardDefaults (androidx.compose.material.icons.outlined  Close (androidx.compose.material.icons.outlined  Color (androidx.compose.material.icons.outlined  Column (androidx.compose.material.icons.outlined  
Composable (androidx.compose.material.icons.outlined  Divider (androidx.compose.material.icons.outlined  
FontWeight (androidx.compose.material.icons.outlined  Icon (androidx.compose.material.icons.outlined  
IconButton (androidx.compose.material.icons.outlined  Icons (androidx.compose.material.icons.outlined  Modifier (androidx.compose.material.icons.outlined  OutlinedTextField (androidx.compose.material.icons.outlined  OutlinedTextFieldDefaults (androidx.compose.material.icons.outlined  
PaddingValues (androidx.compose.material.icons.outlined  Refresh (androidx.compose.material.icons.outlined  RoundedCornerShape (androidx.compose.material.icons.outlined  Row (androidx.compose.material.icons.outlined  Save (androidx.compose.material.icons.outlined  Schedule (androidx.compose.material.icons.outlined  SettingItem (androidx.compose.material.icons.outlined  SettingSection (androidx.compose.material.icons.outlined  SimCard (androidx.compose.material.icons.outlined  Spacer (androidx.compose.material.icons.outlined  String (androidx.compose.material.icons.outlined  Text (androidx.compose.material.icons.outlined  Timer (androidx.compose.material.icons.outlined  Unit (androidx.compose.material.icons.outlined  align (androidx.compose.material.icons.outlined  android (androidx.compose.material.icons.outlined  androidx (androidx.compose.material.icons.outlined  
background (androidx.compose.material.icons.outlined  buttonColors (androidx.compose.material.icons.outlined  
cardColors (androidx.compose.material.icons.outlined  
cardElevation (androidx.compose.material.icons.outlined  colors (androidx.compose.material.icons.outlined  com (androidx.compose.material.icons.outlined  contains (androidx.compose.material.icons.outlined  
fillMaxHeight (androidx.compose.material.icons.outlined  fillMaxSize (androidx.compose.material.icons.outlined  fillMaxWidth (androidx.compose.material.icons.outlined  forEach (androidx.compose.material.icons.outlined  getAvailableSims (androidx.compose.material.icons.outlined  getValue (androidx.compose.material.icons.outlined  height (androidx.compose.material.icons.outlined  isEmpty (androidx.compose.material.icons.outlined  
isNotEmpty (androidx.compose.material.icons.outlined  mutableStateOf (androidx.compose.material.icons.outlined  padding (androidx.compose.material.icons.outlined  provideDelegate (androidx.compose.material.icons.outlined  remember (androidx.compose.material.icons.outlined  rememberScrollState (androidx.compose.material.icons.outlined  setValue (androidx.compose.material.icons.outlined  size (androidx.compose.material.icons.outlined  spacedBy (androidx.compose.material.icons.outlined  toInt (androidx.compose.material.icons.outlined  toIntOrNull (androidx.compose.material.icons.outlined  verticalScroll (androidx.compose.material.icons.outlined  weight (androidx.compose.material.icons.outlined  width (androidx.compose.material.icons.outlined  compose 1androidx.compose.material.icons.outlined.androidx  ui 9androidx.compose.material.icons.outlined.androidx.compose  graphics <androidx.compose.material.icons.outlined.androidx.compose.ui  vector Eandroidx.compose.material.icons.outlined.androidx.compose.ui.graphics  ImageVector Landroidx.compose.material.icons.outlined.androidx.compose.ui.graphics.vector  AboutScreen androidx.compose.material3  
AccessTime androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AppSettings androidx.compose.material3  Arrangement androidx.compose.material3  BasicAlertDialog androidx.compose.material3  Boolean androidx.compose.material3  BottomAppBar androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Checkbox androidx.compose.material3  CheckboxColors androidx.compose.material3  CheckboxDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Close androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Customer androidx.compose.material3  Date androidx.compose.material3  Divider androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  Environment androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  File androidx.compose.material3  FileItem androidx.compose.material3  FileItemRow androidx.compose.material3  
FontWeight androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  Int androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  ListItem androidx.compose.material3  Locale androidx.compose.material3  Log androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  MenuAnchorType androidx.compose.material3  MessageTemplate androidx.compose.material3  Modifier androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
PaddingValues androidx.compose.material3  R androidx.compose.material3  RadioButton androidx.compose.material3  RadioButtonColors androidx.compose.material3  RadioButtonDefaults androidx.compose.material3  Refresh androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Save androidx.compose.material3  Scaffold androidx.compose.material3  Schedule androidx.compose.material3  SecurityException androidx.compose.material3  SettingItem androidx.compose.material3  SettingSection androidx.compose.material3  SimCard androidx.compose.material3  
SimManager androidx.compose.material3  
SmsRepository androidx.compose.material3  
SmsSession androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  TemplateManager androidx.compose.material3  TemplateSelectionItem androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  	TextField androidx.compose.material3  TextFieldColors androidx.compose.material3  Timer androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  any androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  	clickable androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  	compareBy androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  endsWith androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  find androidx.compose.material3  first androidx.compose.material3  forEach androidx.compose.material3  getAvailableSims androidx.compose.material3  getDefaultTemplates androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  	lowercase androidx.compose.material3  map androidx.compose.material3  
mapNotNull androidx.compose.material3  mutableStateOf androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  painterResource androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberScrollState androidx.compose.material3  replace androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  
sortedWith androidx.compose.material3  spacedBy androidx.compose.material3  
startsWith androidx.compose.material3  take androidx.compose.material3  takeIf androidx.compose.material3  textButtonColors androidx.compose.material3  thenBy androidx.compose.material3  toInt androidx.compose.material3  toIntOrNull androidx.compose.material3  trim androidx.compose.material3  verticalScroll androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  textButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  colors +androidx.compose.material3.CheckboxDefaults  error &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ListItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  MenuAnchorType 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  	TextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  dp 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  heightIn 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  sp 6androidx.compose.material3.ExposedDropdownMenuBoxScope  take 6androidx.compose.material3.ExposedDropdownMenuBoxScope  textFieldColors 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  textFieldColors 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	Companion )androidx.compose.material3.MenuAnchorType  PrimaryNotEditable )androidx.compose.material3.MenuAnchorType  PrimaryNotEditable 3androidx.compose.material3.MenuAnchorType.Companion  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors .androidx.compose.material3.RadioButtonDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  AboutScreen androidx.compose.runtime  
AccessTime androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Anchor androidx.compose.runtime  AppSettings androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Close androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Customer androidx.compose.runtime  Date androidx.compose.runtime  Divider androidx.compose.runtime  Environment androidx.compose.runtime  	Exception androidx.compose.runtime  File androidx.compose.runtime  FileItem androidx.compose.runtime  FileItemRow androidx.compose.runtime  
FontWeight androidx.compose.runtime  HorizontalDivider androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  Locale androidx.compose.runtime  Log androidx.compose.runtime  Long androidx.compose.runtime  MessageTemplate androidx.compose.runtime  Modifier androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  R androidx.compose.runtime  RadioButton androidx.compose.runtime  RadioButtonDefaults androidx.compose.runtime  Refresh androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Save androidx.compose.runtime  Schedule androidx.compose.runtime  SecurityException androidx.compose.runtime  SettingItem androidx.compose.runtime  SettingSection androidx.compose.runtime  SimCard androidx.compose.runtime  
SimManager androidx.compose.runtime  
SmsRepository androidx.compose.runtime  
SmsSession androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  TemplateManager androidx.compose.runtime  TemplateSelectionItem androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  Timer androidx.compose.runtime  Unit androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  any androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  	clickable androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  	compareBy androidx.compose.runtime  contains androidx.compose.runtime  delay androidx.compose.runtime  	emptyList androidx.compose.runtime  endsWith androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  find androidx.compose.runtime  first androidx.compose.runtime  forEach androidx.compose.runtime  getAvailableSims androidx.compose.runtime  getDefaultTemplates androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  let androidx.compose.runtime  	lowercase androidx.compose.runtime  map androidx.compose.runtime  
mapNotNull androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateListOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  painterResource androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberScrollState androidx.compose.runtime  replace androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  
sortedWith androidx.compose.runtime  spacedBy androidx.compose.runtime  
startsWith androidx.compose.runtime  take androidx.compose.runtime  takeIf androidx.compose.runtime  textButtonColors androidx.compose.runtime  thenBy androidx.compose.runtime  toInt androidx.compose.runtime  toIntOrNull androidx.compose.runtime  trim androidx.compose.runtime  verticalScroll androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue (androidx.compose.runtime.MutableIntState  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  value androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  observeAsState !androidx.compose.runtime.livedata  SnapshotStateList "androidx.compose.runtime.snapshots  
CustomerField 4androidx.compose.runtime.snapshots.SnapshotStateList  add 4androidx.compose.runtime.snapshots.SnapshotStateList  addAll 4androidx.compose.runtime.snapshots.SnapshotStateList  apply 4androidx.compose.runtime.snapshots.SnapshotStateList  clear 4androidx.compose.runtime.snapshots.SnapshotStateList  forEachIndexed 4androidx.compose.runtime.snapshots.SnapshotStateList  get 4androidx.compose.runtime.snapshots.SnapshotStateList  map 4androidx.compose.runtime.snapshots.SnapshotStateList  set 4androidx.compose.runtime.snapshots.SnapshotStateList  size 4androidx.compose.runtime.snapshots.SnapshotStateList  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  	CenterEnd androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  	CenterEnd 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  horizontalScroll androidx.compose.ui.Modifier  let androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  widthIn androidx.compose.ui.Modifier  wrapContentHeight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  
menuAnchor &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  pointerInput &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  widthIn &androidx.compose.ui.Modifier.Companion  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  minDimension !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  Blue "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  	LightGray "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Blue ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  	LightGray ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  
CustomerField 3androidx.compose.ui.input.pointer.PointerInputScope  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  LocalContext androidx.compose.ui.platform  painterResource androidx.compose.ui.res  AnnotatedString androidx.compose.ui.text  	TextStyle androidx.compose.ui.text  
capitalize androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Number +androidx.compose.ui.text.input.KeyboardType  Phone +androidx.compose.ui.text.input.KeyboardType  Text +androidx.compose.ui.text.input.KeyboardType  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  Phone 5androidx.compose.ui.text.input.KeyboardType.Companion  Text 5androidx.compose.ui.text.input.KeyboardType.Companion  Locale androidx.compose.ui.text.intl  	Companion $androidx.compose.ui.text.intl.Locale  current $androidx.compose.ui.text.intl.Locale  current .androidx.compose.ui.text.intl.Locale.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  End (androidx.compose.ui.text.style.TextAlign  Start (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  End 2androidx.compose.ui.text.style.TextAlign.Companion  Start 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  NotificationCompat androidx.core.app  AutoSmsDisabler #androidx.core.app.ComponentActivity  BackHandler #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PermissionChecker #androidx.core.app.ComponentActivity  SendMessageViewModel #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  SmsAppTheme #androidx.core.app.ComponentActivity  
SmsService #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  
hiltViewModel #androidx.core.app.ComponentActivity  initializeSafeMode #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  observeAsState #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  remember #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  CATEGORY_SERVICE $androidx.core.app.NotificationCompat  PRIORITY_MIN $androidx.core.app.NotificationCompat  VISIBILITY_SECRET $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setShowWhen ,androidx.core.app.NotificationCompat.Builder  	setSilent ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  FileProvider androidx.core.content  edit androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  startForegroundService #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  
hiltViewModel  androidx.hilt.navigation.compose  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  getApplication #androidx.lifecycle.AndroidViewModel  	onCleared #androidx.lifecycle.AndroidViewModel  observeAsState androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  	postValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  	onCleared androidx.lifecycle.ViewModel  BuildConfig com.example.sms_app  R com.example.sms_app  
APP_SIGNATURE com.example.sms_app.BuildConfig  ENABLE_INTEGRITY_CHECK com.example.sms_app.BuildConfig  ENABLE_TAMPER_DETECTION com.example.sms_app.BuildConfig  VERSION_NAME com.example.sms_app.BuildConfig  ic_app_logo com.example.sms_app.R.drawable  Boolean com.example.sms_app.api  GET com.example.sms_app.api  Int com.example.sms_app.api  Keep com.example.sms_app.api  Long com.example.sms_app.api  Response com.example.sms_app.api  ResponseBody com.example.sms_app.api  	Streaming com.example.sms_app.api  String com.example.sms_app.api  UpdateApiService com.example.sms_app.api  VersionResponse com.example.sms_app.api  downloadApk (com.example.sms_app.api.UpdateApiService  getLatestVersion (com.example.sms_app.api.UpdateApiService  fileSize 'com.example.sms_app.api.VersionResponse  forceUpdate 'com.example.sms_app.api.VersionResponse  releaseNotes 'com.example.sms_app.api.VersionResponse  version 'com.example.sms_app.api.VersionResponse  versionCode 'com.example.sms_app.api.VersionResponse  AppSettings com.example.sms_app.data  ApplicationContext com.example.sms_app.data  Boolean com.example.sms_app.data  Context com.example.sms_app.data  Customer com.example.sms_app.data  Date com.example.sms_app.data  	Exception com.example.sms_app.data  Gson com.example.sms_app.data  Inject com.example.sms_app.data  Int com.example.sms_app.data  KEY_APP_SETTINGS com.example.sms_app.data  
KEY_CONFIG com.example.sms_app.data  KEY_COUNTDOWN_CUSTOMER_COUNT com.example.sms_app.data  KEY_COUNTDOWN_START_TIME com.example.sms_app.data  KEY_COUNTDOWN_TOTAL_TIME com.example.sms_app.data  
KEY_CUSTOMERS com.example.sms_app.data  KEY_DEFAULT_TEMPLATE com.example.sms_app.data  KEY_MESSAGE_TEMPLATES com.example.sms_app.data  KEY_SELECTED_SIM com.example.sms_app.data  
KEY_TEMPLATES com.example.sms_app.data  List com.example.sms_app.data  Locale com.example.sms_app.data  Log com.example.sms_app.data  Long com.example.sms_app.data  MAX_SMS_PER_DAY com.example.sms_app.data  Map com.example.sms_app.data  MessageTemplate com.example.sms_app.data  MutableList com.example.sms_app.data  Pair com.example.sms_app.data  
SessionBackup com.example.sms_app.data  SharedPreferences com.example.sms_app.data  SimpleDateFormat com.example.sms_app.data  	SmsConfig com.example.sms_app.data  SmsProgress com.example.sms_app.data  
SmsRepository com.example.sms_app.data  
SmsSession com.example.sms_app.data  SmsTemplate com.example.sms_app.data  String com.example.sms_app.data  System com.example.sms_app.data  TemplateManager com.example.sms_app.data  	TypeToken com.example.sms_app.data  UUID com.example.sms_app.data  android com.example.sms_app.data  
component1 com.example.sms_app.data  
component2 com.example.sms_app.data  contains com.example.sms_app.data  edit com.example.sms_app.data  	emptyList com.example.sms_app.data  filter com.example.sms_app.data  find com.example.sms_app.data  first com.example.sms_app.data  forEach com.example.sms_app.data  getDefaultTemplates com.example.sms_app.data  getLastSmsDateKey com.example.sms_app.data  getSmsCountKey com.example.sms_app.data  getTemplateById com.example.sms_app.data  getValue com.example.sms_app.data  isEmpty com.example.sms_app.data  
isNotEmpty com.example.sms_app.data  java com.example.sms_app.data  lazy com.example.sms_app.data  listOf com.example.sms_app.data  
mutableListOf com.example.sms_app.data  provideDelegate com.example.sms_app.data  	removeAll com.example.sms_app.data  replace com.example.sms_app.data  
startsWith com.example.sms_app.data  take com.example.sms_app.data  
toMutableList com.example.sms_app.data  
SwitchSetting $com.example.sms_app.data.AppSettings  clearCacheAfterImport $com.example.sms_app.data.AppSettings  com $com.example.sms_app.data.AppSettings  copy $com.example.sms_app.data.AppSettings  
customerLimit $com.example.sms_app.data.AppSettings  edit $com.example.sms_app.data.AppSettings  
enableAutoSms $com.example.sms_app.data.AppSettings  enableUpdate $com.example.sms_app.data.AppSettings  intervalBetweenSmsSeconds $com.example.sms_app.data.AppSettings  isLimitCustomer $com.example.sms_app.data.AppSettings  maxIntervalSeconds $com.example.sms_app.data.AppSettings  maxRetryAttempts $com.example.sms_app.data.AppSettings  minIntervalSeconds $com.example.sms_app.data.AppSettings  retryDelaySeconds $com.example.sms_app.data.AppSettings  Boolean !com.example.sms_app.data.Customer  Int !com.example.sms_app.data.Customer  List !com.example.sms_app.data.Customer  MessageTemplate !com.example.sms_app.data.Customer  String !com.example.sms_app.data.Customer  TemplateManager !com.example.sms_app.data.Customer  address !com.example.sms_app.data.Customer  carrier !com.example.sms_app.data.Customer  copy !com.example.sms_app.data.Customer  
detectCarrier !com.example.sms_app.data.Customer  getPersonalizedMessage !com.example.sms_app.data.Customer  getTemplateById !com.example.sms_app.data.Customer  id !com.example.sms_app.data.Customer  idNumber !com.example.sms_app.data.Customer  
isSelected !com.example.sms_app.data.Customer  let !com.example.sms_app.data.Customer  name !com.example.sms_app.data.Customer  option1 !com.example.sms_app.data.Customer  option2 !com.example.sms_app.data.Customer  option3 !com.example.sms_app.data.Customer  option4 !com.example.sms_app.data.Customer  option5 !com.example.sms_app.data.Customer  phoneNumber !com.example.sms_app.data.Customer  replace !com.example.sms_app.data.Customer  
startsWith !com.example.sms_app.data.Customer  templateNumber !com.example.sms_app.data.Customer  TemplateManager +com.example.sms_app.data.Customer.Companion  
detectCarrier +com.example.sms_app.data.Customer.Companion  getTemplateById +com.example.sms_app.data.Customer.Companion  replace +com.example.sms_app.data.Customer.Companion  
startsWith +com.example.sms_app.data.Customer.Companion  content (com.example.sms_app.data.MessageTemplate  copy (com.example.sms_app.data.MessageTemplate  description (com.example.sms_app.data.MessageTemplate  id (com.example.sms_app.data.MessageTemplate  ACTIVE_SESSION_KEY &com.example.sms_app.data.SessionBackup  Context &com.example.sms_app.data.SessionBackup  Date &com.example.sms_app.data.SessionBackup  Gson &com.example.sms_app.data.SessionBackup  Locale &com.example.sms_app.data.SessionBackup  Log &com.example.sms_app.data.SessionBackup  MAX_HISTORY_ITEMS &com.example.sms_app.data.SessionBackup  
PREFS_NAME &com.example.sms_app.data.SessionBackup  SESSION_HISTORY_KEY &com.example.sms_app.data.SessionBackup  SimpleDateFormat &com.example.sms_app.data.SessionBackup  
SmsSession &com.example.sms_app.data.SessionBackup  System &com.example.sms_app.data.SessionBackup  TAG &com.example.sms_app.data.SessionBackup  UUID &com.example.sms_app.data.SessionBackup  addToHistory &com.example.sms_app.data.SessionBackup  clearActiveSession &com.example.sms_app.data.SessionBackup  clearAllSessionHistory &com.example.sms_app.data.SessionBackup  completeSession &com.example.sms_app.data.SessionBackup  context &com.example.sms_app.data.SessionBackup  	emptyList &com.example.sms_app.data.SessionBackup  filter &com.example.sms_app.data.SessionBackup  find &com.example.sms_app.data.SessionBackup  first &com.example.sms_app.data.SessionBackup  generateDefaultSessionName &com.example.sms_app.data.SessionBackup  generateSessionId &com.example.sms_app.data.SessionBackup  getActiveSession &com.example.sms_app.data.SessionBackup  getSessionHistory &com.example.sms_app.data.SessionBackup  getValue &com.example.sms_app.data.SessionBackup  gson &com.example.sms_app.data.SessionBackup  isEmpty &com.example.sms_app.data.SessionBackup  
isNotEmpty &com.example.sms_app.data.SessionBackup  java &com.example.sms_app.data.SessionBackup  lazy &com.example.sms_app.data.SessionBackup  
mutableListOf &com.example.sms_app.data.SessionBackup  provideDelegate &com.example.sms_app.data.SessionBackup  	removeAll &com.example.sms_app.data.SessionBackup  saveActiveSession &com.example.sms_app.data.SessionBackup  sharedPreferences &com.example.sms_app.data.SessionBackup  
startsWith &com.example.sms_app.data.SessionBackup  take &com.example.sms_app.data.SessionBackup  
toMutableList &com.example.sms_app.data.SessionBackup  let $com.example.sms_app.data.SmsProgress  message $com.example.sms_app.data.SmsProgress  progress $com.example.sms_app.data.SmsProgress  total $com.example.sms_app.data.SmsProgress  AppSettings &com.example.sms_app.data.SmsRepository  ApplicationContext &com.example.sms_app.data.SmsRepository  Boolean &com.example.sms_app.data.SmsRepository  Context &com.example.sms_app.data.SmsRepository  Customer &com.example.sms_app.data.SmsRepository  	Exception &com.example.sms_app.data.SmsRepository  Gson &com.example.sms_app.data.SmsRepository  Inject &com.example.sms_app.data.SmsRepository  Int &com.example.sms_app.data.SmsRepository  KEY_APP_SETTINGS &com.example.sms_app.data.SmsRepository  
KEY_CONFIG &com.example.sms_app.data.SmsRepository  KEY_COUNTDOWN_CUSTOMER_COUNT &com.example.sms_app.data.SmsRepository  KEY_COUNTDOWN_START_TIME &com.example.sms_app.data.SmsRepository  KEY_COUNTDOWN_TOTAL_TIME &com.example.sms_app.data.SmsRepository  
KEY_CUSTOMERS &com.example.sms_app.data.SmsRepository  KEY_DEFAULT_TEMPLATE &com.example.sms_app.data.SmsRepository  KEY_LAST_SMS_DATE &com.example.sms_app.data.SmsRepository  KEY_MESSAGE_TEMPLATES &com.example.sms_app.data.SmsRepository  KEY_SELECTED_SIM &com.example.sms_app.data.SmsRepository  KEY_SMS_COUNT_TODAY &com.example.sms_app.data.SmsRepository  
KEY_TEMPLATES &com.example.sms_app.data.SmsRepository  List &com.example.sms_app.data.SmsRepository  Long &com.example.sms_app.data.SmsRepository  MAX_SMS_PER_DAY &com.example.sms_app.data.SmsRepository  MessageTemplate &com.example.sms_app.data.SmsRepository  Pair &com.example.sms_app.data.SmsRepository  
SessionBackup &com.example.sms_app.data.SmsRepository  	SmsConfig &com.example.sms_app.data.SmsRepository  SmsTemplate &com.example.sms_app.data.SmsRepository  TemplateManager &com.example.sms_app.data.SmsRepository  	TypeToken &com.example.sms_app.data.SmsRepository  _default &com.example.sms_app.data.SmsRepository  _messageTemplate &com.example.sms_app.data.SmsRepository  _selectedSim &com.example.sms_app.data.SmsRepository  also &com.example.sms_app.data.SmsRepository  android &com.example.sms_app.data.SmsRepository  apply &com.example.sms_app.data.SmsRepository  clearCountdownData &com.example.sms_app.data.SmsRepository  
component1 &com.example.sms_app.data.SmsRepository  
component2 &com.example.sms_app.data.SmsRepository  contains &com.example.sms_app.data.SmsRepository  debugPrintAllSmsCountValues &com.example.sms_app.data.SmsRepository  edit &com.example.sms_app.data.SmsRepository  filter &com.example.sms_app.data.SmsRepository  forceRefreshSmsCount &com.example.sms_app.data.SmsRepository  getAppSettings &com.example.sms_app.data.SmsRepository  getApplication &com.example.sms_app.data.SmsRepository  getCountdownStartTime &com.example.sms_app.data.SmsRepository  getCustomers &com.example.sms_app.data.SmsRepository  getDefaultCustomers &com.example.sms_app.data.SmsRepository  getDefaultTemplate &com.example.sms_app.data.SmsRepository  getDefaultTemplates &com.example.sms_app.data.SmsRepository  
getDualSimIds &com.example.sms_app.data.SmsRepository  getLastSmsDateKey &com.example.sms_app.data.SmsRepository  getMessageTemplates &com.example.sms_app.data.SmsRepository  getSelectedSim &com.example.sms_app.data.SmsRepository  getSimForCustomer &com.example.sms_app.data.SmsRepository  getSmsCountKey &com.example.sms_app.data.SmsRepository  getSmsCountToday &com.example.sms_app.data.SmsRepository  getTemplates &com.example.sms_app.data.SmsRepository  gson &com.example.sms_app.data.SmsRepository  incrementSmsCount &com.example.sms_app.data.SmsRepository  isDualSimEnabled &com.example.sms_app.data.SmsRepository  java &com.example.sms_app.data.SmsRepository  listOf &com.example.sms_app.data.SmsRepository  prefs &com.example.sms_app.data.SmsRepository  resetAllSimCounts &com.example.sms_app.data.SmsRepository  
resetSmsCount &com.example.sms_app.data.SmsRepository  saveAppSettings &com.example.sms_app.data.SmsRepository  saveCountdownData &com.example.sms_app.data.SmsRepository  
saveCustomers &com.example.sms_app.data.SmsRepository  saveMessageTemplates &com.example.sms_app.data.SmsRepository  setDefaultTemplate &com.example.sms_app.data.SmsRepository  setDualSimConfig &com.example.sms_app.data.SmsRepository  setSelectedSim &com.example.sms_app.data.SmsRepository  shouldSendParallelToDualSim &com.example.sms_app.data.SmsRepository  sortedBy &com.example.sms_app.data.SmsRepository  
toMutableList &com.example.sms_app.data.SmsRepository  AppSettings 0com.example.sms_app.data.SmsRepository.Companion  Context 0com.example.sms_app.data.SmsRepository.Companion  Gson 0com.example.sms_app.data.SmsRepository.Companion  KEY_APP_SETTINGS 0com.example.sms_app.data.SmsRepository.Companion  
KEY_CONFIG 0com.example.sms_app.data.SmsRepository.Companion  KEY_COUNTDOWN_CUSTOMER_COUNT 0com.example.sms_app.data.SmsRepository.Companion  KEY_COUNTDOWN_START_TIME 0com.example.sms_app.data.SmsRepository.Companion  KEY_COUNTDOWN_TOTAL_TIME 0com.example.sms_app.data.SmsRepository.Companion  
KEY_CUSTOMERS 0com.example.sms_app.data.SmsRepository.Companion  KEY_DEFAULT_TEMPLATE 0com.example.sms_app.data.SmsRepository.Companion  KEY_LAST_SMS_DATE 0com.example.sms_app.data.SmsRepository.Companion  KEY_MESSAGE_TEMPLATES 0com.example.sms_app.data.SmsRepository.Companion  KEY_SELECTED_SIM 0com.example.sms_app.data.SmsRepository.Companion  KEY_SMS_COUNT_TODAY 0com.example.sms_app.data.SmsRepository.Companion  
KEY_TEMPLATES 0com.example.sms_app.data.SmsRepository.Companion  MAX_SMS_PER_DAY 0com.example.sms_app.data.SmsRepository.Companion  Pair 0com.example.sms_app.data.SmsRepository.Companion  	SmsConfig 0com.example.sms_app.data.SmsRepository.Companion  TemplateManager 0com.example.sms_app.data.SmsRepository.Companion  android 0com.example.sms_app.data.SmsRepository.Companion  
component1 0com.example.sms_app.data.SmsRepository.Companion  
component2 0com.example.sms_app.data.SmsRepository.Companion  contains 0com.example.sms_app.data.SmsRepository.Companion  edit 0com.example.sms_app.data.SmsRepository.Companion  getDefaultTemplates 0com.example.sms_app.data.SmsRepository.Companion  getLastSmsDateKey 0com.example.sms_app.data.SmsRepository.Companion  getSmsCountKey 0com.example.sms_app.data.SmsRepository.Companion  java 0com.example.sms_app.data.SmsRepository.Companion  listOf 0com.example.sms_app.data.SmsRepository.Companion  completedCustomers #com.example.sms_app.data.SmsSession  failedCustomerId #com.example.sms_app.data.SmsSession  failedReason #com.example.sms_app.data.SmsSession  lastUpdateTime #com.example.sms_app.data.SmsSession  remainingCustomers #com.example.sms_app.data.SmsSession  	sentCount #com.example.sms_app.data.SmsSession  	sessionId #com.example.sms_app.data.SmsSession  sessionName #com.example.sms_app.data.SmsSession  	startTime #com.example.sms_app.data.SmsSession  status #com.example.sms_app.data.SmsSession  totalCustomers #com.example.sms_app.data.SmsSession  
component1 $com.example.sms_app.data.SmsTemplate  
component2 $com.example.sms_app.data.SmsTemplate  content $com.example.sms_app.data.SmsTemplate  id $com.example.sms_app.data.SmsTemplate  name $com.example.sms_app.data.SmsTemplate  replace $com.example.sms_app.data.SmsTemplate  MessageTemplate (com.example.sms_app.data.TemplateManager  find (com.example.sms_app.data.TemplateManager  getDefaultTemplates (com.example.sms_app.data.TemplateManager  getTemplateById (com.example.sms_app.data.TemplateManager  listOf (com.example.sms_app.data.TemplateManager  	AppModule com.example.sms_app.di  ApplicationContext com.example.sms_app.di  Context com.example.sms_app.di  	InstallIn com.example.sms_app.di  Module com.example.sms_app.di  Provides com.example.sms_app.di  	Singleton com.example.sms_app.di  SingletonComponent com.example.sms_app.di  Activity  com.example.sms_app.presentation  ActivityResultContracts  com.example.sms_app.presentation  Application  com.example.sms_app.presentation  Boolean  com.example.sms_app.presentation  Build  com.example.sms_app.presentation  
Composable  com.example.sms_app.presentation  Environment  com.example.sms_app.presentation  	Exception  com.example.sms_app.presentation  File  com.example.sms_app.presentation  HiltAndroidApp  com.example.sms_app.presentation  Int  com.example.sms_app.presentation  Intent  com.example.sms_app.presentation  List  com.example.sms_app.presentation  Log  com.example.sms_app.presentation  PermissionChecker  com.example.sms_app.presentation  Settings  com.example.sms_app.presentation  SmsApplication  com.example.sms_app.presentation  String  com.example.sms_app.presentation  	Throwable  com.example.sms_app.presentation  Timber  com.example.sms_app.presentation  Unit  com.example.sms_app.presentation  
appendText  com.example.sms_app.presentation  applicationContext  com.example.sms_app.presentation  filter  com.example.sms_app.presentation  i  com.example.sms_app.presentation  indexOfFirst  com.example.sms_app.presentation  listOf  com.example.sms_app.presentation  map  com.example.sms_app.presentation  minusAssign  com.example.sms_app.presentation  plant  com.example.sms_app.presentation  provideDelegate  com.example.sms_app.presentation  
toMutableList  com.example.sms_app.presentation  File /com.example.sms_app.presentation.SmsApplication  Log /com.example.sms_app.presentation.SmsApplication  Timber /com.example.sms_app.presentation.SmsApplication  
appendText /com.example.sms_app.presentation.SmsApplication  applicationContext /com.example.sms_app.presentation.SmsApplication  i /com.example.sms_app.presentation.SmsApplication  plant /com.example.sms_app.presentation.SmsApplication  	DebugTree 'com.example.sms_app.presentation.Timber  AndroidEntryPoint )com.example.sms_app.presentation.activity  AutoSmsDisabler )com.example.sms_app.presentation.activity  BackHandler )com.example.sms_app.presentation.activity  Build )com.example.sms_app.presentation.activity  Bundle )com.example.sms_app.presentation.activity  Intent )com.example.sms_app.presentation.activity  MainActivity )com.example.sms_app.presentation.activity  
MainScreen )com.example.sms_app.presentation.activity  Manifest )com.example.sms_app.presentation.activity  PermissionChecker )com.example.sms_app.presentation.activity  SendMessageViewModel )com.example.sms_app.presentation.activity  Settings )com.example.sms_app.presentation.activity  SmsAppTheme )com.example.sms_app.presentation.activity  
SmsService )com.example.sms_app.presentation.activity  SuppressLint )com.example.sms_app.presentation.activity  Surface )com.example.sms_app.presentation.activity  Toast )com.example.sms_app.presentation.activity  android )com.example.sms_app.presentation.activity  androidx )com.example.sms_app.presentation.activity  apply )com.example.sms_app.presentation.activity  getValue )com.example.sms_app.presentation.activity  
hiltViewModel )com.example.sms_app.presentation.activity  initializeSafeMode )com.example.sms_app.presentation.activity  java )com.example.sms_app.presentation.activity  listOf )com.example.sms_app.presentation.activity  mutableStateOf )com.example.sms_app.presentation.activity  observeAsState )com.example.sms_app.presentation.activity  provideDelegate )com.example.sms_app.presentation.activity  remember )com.example.sms_app.presentation.activity  setValue )com.example.sms_app.presentation.activity  AutoSmsDisabler 6com.example.sms_app.presentation.activity.MainActivity  BackHandler 6com.example.sms_app.presentation.activity.MainActivity  Build 6com.example.sms_app.presentation.activity.MainActivity  Intent 6com.example.sms_app.presentation.activity.MainActivity  
MainScreen 6com.example.sms_app.presentation.activity.MainActivity  Manifest 6com.example.sms_app.presentation.activity.MainActivity  PermissionChecker 6com.example.sms_app.presentation.activity.MainActivity  Settings 6com.example.sms_app.presentation.activity.MainActivity  SmsAppTheme 6com.example.sms_app.presentation.activity.MainActivity  
SmsService 6com.example.sms_app.presentation.activity.MainActivity  Surface 6com.example.sms_app.presentation.activity.MainActivity  Toast 6com.example.sms_app.presentation.activity.MainActivity  android 6com.example.sms_app.presentation.activity.MainActivity  apply 6com.example.sms_app.presentation.activity.MainActivity  finish 6com.example.sms_app.presentation.activity.MainActivity  getValue 6com.example.sms_app.presentation.activity.MainActivity  
hiltViewModel 6com.example.sms_app.presentation.activity.MainActivity  initializeSafeMode 6com.example.sms_app.presentation.activity.MainActivity  java 6com.example.sms_app.presentation.activity.MainActivity  listOf 6com.example.sms_app.presentation.activity.MainActivity  mutableStateOf 6com.example.sms_app.presentation.activity.MainActivity  observeAsState 6com.example.sms_app.presentation.activity.MainActivity  provideDelegate 6com.example.sms_app.presentation.activity.MainActivity  remember 6com.example.sms_app.presentation.activity.MainActivity  
setContent 6com.example.sms_app.presentation.activity.MainActivity  setValue 6com.example.sms_app.presentation.activity.MainActivity  stopService 6com.example.sms_app.presentation.activity.MainActivity  activity 2com.example.sms_app.presentation.activity.androidx  ComponentActivity ;com.example.sms_app.presentation.activity.androidx.activity  AddCustomerDialog *com.example.sms_app.presentation.component  AddCustomerViewModel *com.example.sms_app.presentation.component  AlertDialog *com.example.sms_app.presentation.component  	Alignment *com.example.sms_app.presentation.component  AppSettings *com.example.sms_app.presentation.component  AppUpdateManager *com.example.sms_app.presentation.component  Arrangement *com.example.sms_app.presentation.component  BackUpDialog *com.example.sms_app.presentation.component  BackUpViewModel *com.example.sms_app.presentation.component  Boolean *com.example.sms_app.presentation.component  BorderStroke *com.example.sms_app.presentation.component  BottomAppBar *com.example.sms_app.presentation.component  BottomButton *com.example.sms_app.presentation.component  Box *com.example.sms_app.presentation.component  BuildConfig *com.example.sms_app.presentation.component  Button *com.example.sms_app.presentation.component  ButtonDefaults *com.example.sms_app.presentation.component  Card *com.example.sms_app.presentation.component  CardDefaults *com.example.sms_app.presentation.component  Checkbox *com.example.sms_app.presentation.component  CircularProgressIndicator *com.example.sms_app.presentation.component  Color *com.example.sms_app.presentation.component  Column *com.example.sms_app.presentation.component  
Composable *com.example.sms_app.presentation.component  CoroutineScope *com.example.sms_app.presentation.component  Customer *com.example.sms_app.presentation.component  
CustomerField *com.example.sms_app.presentation.component  Date *com.example.sms_app.presentation.component  Delay *com.example.sms_app.presentation.component  Dispatchers *com.example.sms_app.presentation.component  Divider *com.example.sms_app.presentation.component  DropdownMenuItem *com.example.sms_app.presentation.component  EditValueDialog *com.example.sms_app.presentation.component  Environment *com.example.sms_app.presentation.component  	Exception *com.example.sms_app.presentation.component  ExperimentalMaterial3Api *com.example.sms_app.presentation.component  ExposedDropdownMenuBox *com.example.sms_app.presentation.component  ExposedDropdownMenuDefaults *com.example.sms_app.presentation.component  File *com.example.sms_app.presentation.component  FileItem *com.example.sms_app.presentation.component  FileItemRow *com.example.sms_app.presentation.component  Float *com.example.sms_app.presentation.component  FolderSelectionDialog *com.example.sms_app.presentation.component  
FontWeight *com.example.sms_app.presentation.component  HorizontalDivider *com.example.sms_app.presentation.component  Icon *com.example.sms_app.presentation.component  
IconButton *com.example.sms_app.presentation.component  Icons *com.example.sms_app.presentation.component  ImageVector *com.example.sms_app.presentation.component  Int *com.example.sms_app.presentation.component  KeyboardOptions *com.example.sms_app.presentation.component  KeyboardType *com.example.sms_app.presentation.component  LaunchedEffect *com.example.sms_app.presentation.component  
LazyColumn *com.example.sms_app.presentation.component  LazyRow *com.example.sms_app.presentation.component  Limit *com.example.sms_app.presentation.component  LinearProgressIndicator *com.example.sms_app.presentation.component  List *com.example.sms_app.presentation.component  ListItem *com.example.sms_app.presentation.component  Locale *com.example.sms_app.presentation.component  Long *com.example.sms_app.presentation.component  
MaterialTheme *com.example.sms_app.presentation.component  MenuAnchorType *com.example.sms_app.presentation.component  MessageTemplate *com.example.sms_app.presentation.component  Modifier *com.example.sms_app.presentation.component  MoreVertFunctions *com.example.sms_app.presentation.component  MoreView *com.example.sms_app.presentation.component  MyBottomBar *com.example.sms_app.presentation.component  MyTopBar *com.example.sms_app.presentation.component  
NumSetting *com.example.sms_app.presentation.component  OptIn *com.example.sms_app.presentation.component  OutlinedTextField *com.example.sms_app.presentation.component  OutlinedTextFieldDefaults *com.example.sms_app.presentation.component  
PaddingValues *com.example.sms_app.presentation.component  
PatternDialog *com.example.sms_app.presentation.component  PatternViewModel *com.example.sms_app.presentation.component  RadioButton *com.example.sms_app.presentation.component  RoundedCornerShape *com.example.sms_app.presentation.component  Row *com.example.sms_app.presentation.component  Scaffold *com.example.sms_app.presentation.component  SearchDialog *com.example.sms_app.presentation.component  SecurityException *com.example.sms_app.presentation.component  SelectSimDialog *com.example.sms_app.presentation.component  SendMessageDialog *com.example.sms_app.presentation.component  SendMessageViewModel *com.example.sms_app.presentation.component  
SettingDialog *com.example.sms_app.presentation.component  SettingViewModel *com.example.sms_app.presentation.component  	SimConfig *com.example.sms_app.presentation.component  
SimManager *com.example.sms_app.presentation.component  Spacer *com.example.sms_app.presentation.component  String *com.example.sms_app.presentation.component  SuppressLint *com.example.sms_app.presentation.component  Switch *com.example.sms_app.presentation.component  
SwitchSetting *com.example.sms_app.presentation.component  System *com.example.sms_app.presentation.component  TODO *com.example.sms_app.presentation.component  TemplateManager *com.example.sms_app.presentation.component  Text *com.example.sms_app.presentation.component  	TextAlign *com.example.sms_app.presentation.component  
TextButton *com.example.sms_app.presentation.component  	TextField *com.example.sms_app.presentation.component  TextSetting *com.example.sms_app.presentation.component  	TextStyle *com.example.sms_app.presentation.component  Toast *com.example.sms_app.presentation.component  	TopAppBar *com.example.sms_app.presentation.component  TrailingIcon *com.example.sms_app.presentation.component  Unit *com.example.sms_app.presentation.component  Update *com.example.sms_app.presentation.component  UpdateDialog *com.example.sms_app.presentation.component  WrongPatternDialog *com.example.sms_app.presentation.component  android *com.example.sms_app.presentation.component  androidx *com.example.sms_app.presentation.component  any *com.example.sms_app.presentation.component  apply *com.example.sms_app.presentation.component  
associateWith *com.example.sms_app.presentation.component  
background *com.example.sms_app.presentation.component  buttonColors *com.example.sms_app.presentation.component  
capitalize *com.example.sms_app.presentation.component  
cardColors *com.example.sms_app.presentation.component  	clickable *com.example.sms_app.presentation.component  colors *com.example.sms_app.presentation.component  com *com.example.sms_app.presentation.component  	compareBy *com.example.sms_app.presentation.component  contains *com.example.sms_app.presentation.component  count *com.example.sms_app.presentation.component  	emptyList *com.example.sms_app.presentation.component  endsWith *com.example.sms_app.presentation.component  
fillMaxHeight *com.example.sms_app.presentation.component  fillMaxSize *com.example.sms_app.presentation.component  fillMaxWidth *com.example.sms_app.presentation.component  filter *com.example.sms_app.presentation.component  find *com.example.sms_app.presentation.component  first *com.example.sms_app.presentation.component  firstOrNull *com.example.sms_app.presentation.component  forEach *com.example.sms_app.presentation.component  forEachIndexed *com.example.sms_app.presentation.component  format *com.example.sms_app.presentation.component  formatDuration *com.example.sms_app.presentation.component  getAvailableSims *com.example.sms_app.presentation.component  getDefaultTemplates *com.example.sms_app.presentation.component  getOrDefault *com.example.sms_app.presentation.component  	getOrNull *com.example.sms_app.presentation.component  getValue *com.example.sms_app.presentation.component  height *com.example.sms_app.presentation.component  heightIn *com.example.sms_app.presentation.component  horizontalScroll *com.example.sms_app.presentation.component  isBlank *com.example.sms_app.presentation.component  
isNotEmpty *com.example.sms_app.presentation.component  java *com.example.sms_app.presentation.component  kotlinx *com.example.sms_app.presentation.component  launch *com.example.sms_app.presentation.component  let *com.example.sms_app.presentation.component  listOf *com.example.sms_app.presentation.component  	lowercase *com.example.sms_app.presentation.component  map *com.example.sms_app.presentation.component  
mapNotNull *com.example.sms_app.presentation.component  maxOf *com.example.sms_app.presentation.component  
menuAnchor *com.example.sms_app.presentation.component  minus *com.example.sms_app.presentation.component  mutableStateOf *com.example.sms_app.presentation.component  padding *com.example.sms_app.presentation.component  plus *com.example.sms_app.presentation.component  pointerInput *com.example.sms_app.presentation.component  provideDelegate *com.example.sms_app.presentation.component  remember *com.example.sms_app.presentation.component  rememberScrollState *com.example.sms_app.presentation.component  replace *com.example.sms_app.presentation.component  runCatching *com.example.sms_app.presentation.component  setOf *com.example.sms_app.presentation.component  setValue *com.example.sms_app.presentation.component  size *com.example.sms_app.presentation.component  
sortedWith *com.example.sms_app.presentation.component  spacedBy *com.example.sms_app.presentation.component  
startsWith *com.example.sms_app.presentation.component  take *com.example.sms_app.presentation.component  textButtonColors *com.example.sms_app.presentation.component  textFieldColors *com.example.sms_app.presentation.component  thenBy *com.example.sms_app.presentation.component  toIntOrNull *com.example.sms_app.presentation.component  trim *com.example.sms_app.presentation.component  	uppercase *com.example.sms_app.presentation.component  verticalScroll *com.example.sms_app.presentation.component  weight *com.example.sms_app.presentation.component  width *com.example.sms_app.presentation.component  widthIn *com.example.sms_app.presentation.component  
UpdateInfo ;com.example.sms_app.presentation.component.AppUpdateManager  Add 7com.example.sms_app.presentation.component.BottomButton  Icons 7com.example.sms_app.presentation.component.BottomButton  Message 7com.example.sms_app.presentation.component.BottomButton  MoreVert 7com.example.sms_app.presentation.component.BottomButton  None 7com.example.sms_app.presentation.component.BottomButton  	PersonAdd 7com.example.sms_app.presentation.component.BottomButton  Search 7com.example.sms_app.presentation.component.BottomButton  Send 7com.example.sms_app.presentation.component.BottomButton  SendMessage 7com.example.sms_app.presentation.component.BottomButton  Setting 7com.example.sms_app.presentation.component.BottomButton  Settings 7com.example.sms_app.presentation.component.BottomButton  WrongPattern 7com.example.sms_app.presentation.component.BottomButton  icon 7com.example.sms_app.presentation.component.BottomButton  Address 8com.example.sms_app.presentation.component.CustomerField  Call 8com.example.sms_app.presentation.component.CustomerField  DocumentScanner 8com.example.sms_app.presentation.component.CustomerField  Home 8com.example.sms_app.presentation.component.CustomerField  Icons 8com.example.sms_app.presentation.component.CustomerField  Id 8com.example.sms_app.presentation.component.CustomerField  KeyboardType 8com.example.sms_app.presentation.component.CustomerField  Message 8com.example.sms_app.presentation.component.CustomerField  Name 8com.example.sms_app.presentation.component.CustomerField  Notes 8com.example.sms_app.presentation.component.CustomerField  Option1 8com.example.sms_app.presentation.component.CustomerField  Option2 8com.example.sms_app.presentation.component.CustomerField  Option3 8com.example.sms_app.presentation.component.CustomerField  Option4 8com.example.sms_app.presentation.component.CustomerField  Option5 8com.example.sms_app.presentation.component.CustomerField  Pattern 8com.example.sms_app.presentation.component.CustomerField  Person 8com.example.sms_app.presentation.component.CustomerField  PhoneNumber 8com.example.sms_app.presentation.component.CustomerField  default 8com.example.sms_app.presentation.component.CustomerField  entries 8com.example.sms_app.presentation.component.CustomerField  getValue 8com.example.sms_app.presentation.component.CustomerField  icon 8com.example.sms_app.presentation.component.CustomerField  keyboardType 8com.example.sms_app.presentation.component.CustomerField  ordinal 8com.example.sms_app.presentation.component.CustomerField  placeholder 8com.example.sms_app.presentation.component.CustomerField  trim 8com.example.sms_app.presentation.component.CustomerField  isDirectory 3com.example.sms_app.presentation.component.FileItem  lastModified 3com.example.sms_app.presentation.component.FileItem  name 3com.example.sms_app.presentation.component.FileItem  path 3com.example.sms_app.presentation.component.FileItem  size 3com.example.sms_app.presentation.component.FileItem  Call <com.example.sms_app.presentation.component.MoreVertFunctions  
CloudDownload <com.example.sms_app.presentation.component.MoreVertFunctions  	ExitToApp <com.example.sms_app.presentation.component.MoreVertFunctions  Filter <com.example.sms_app.presentation.component.MoreVertFunctions  	FilterAlt <com.example.sms_app.presentation.component.MoreVertFunctions  Home <com.example.sms_app.presentation.component.MoreVertFunctions  Icons <com.example.sms_app.presentation.component.MoreVertFunctions  Out <com.example.sms_app.presentation.component.MoreVertFunctions  Support <com.example.sms_app.presentation.component.MoreVertFunctions  Update <com.example.sms_app.presentation.component.MoreVertFunctions  entries <com.example.sms_app.presentation.component.MoreVertFunctions  icon <com.example.sms_app.presentation.component.MoreVertFunctions  text <com.example.sms_app.presentation.component.MoreVertFunctions  Delay 5com.example.sms_app.presentation.component.NumSetting  Limit 5com.example.sms_app.presentation.component.NumSetting  default 5com.example.sms_app.presentation.component.NumSetting  entries 5com.example.sms_app.presentation.component.NumSetting  getValue 5com.example.sms_app.presentation.component.NumSetting  name 5com.example.sms_app.presentation.component.NumSetting  text 5com.example.sms_app.presentation.component.NumSetting  valueOf 5com.example.sms_app.presentation.component.NumSetting  Limit 8com.example.sms_app.presentation.component.SwitchSetting  Update 8com.example.sms_app.presentation.component.SwitchSetting  default 8com.example.sms_app.presentation.component.SwitchSetting  entries 8com.example.sms_app.presentation.component.SwitchSetting  getValue 8com.example.sms_app.presentation.component.SwitchSetting  text 8com.example.sms_app.presentation.component.SwitchSetting  BuildConfig 6com.example.sms_app.presentation.component.TextSetting  default 6com.example.sms_app.presentation.component.TextSetting  entries 6com.example.sms_app.presentation.component.TextSetting  text 6com.example.sms_app.presentation.component.TextSetting  app 2com.example.sms_app.presentation.component.android  Activity 6com.example.sms_app.presentation.component.android.app  example .com.example.sms_app.presentation.component.com  sms_app 6com.example.sms_app.presentation.component.com.example  data >com.example.sms_app.presentation.component.com.example.sms_app  Customer Ccom.example.sms_app.presentation.component.com.example.sms_app.data  ActivityResultContracts 'com.example.sms_app.presentation.screen  	Alignment 'com.example.sms_app.presentation.screen  Arrangement 'com.example.sms_app.presentation.screen  Box 'com.example.sms_app.presentation.screen  Checkbox 'com.example.sms_app.presentation.screen  CheckboxDefaults 'com.example.sms_app.presentation.screen  Color 'com.example.sms_app.presentation.screen  Column 'com.example.sms_app.presentation.screen  
Composable 'com.example.sms_app.presentation.screen  Dispatchers 'com.example.sms_app.presentation.screen  
FontWeight 'com.example.sms_app.presentation.screen  HorizontalDivider 'com.example.sms_app.presentation.screen  Icon 'com.example.sms_app.presentation.screen  
IconButton 'com.example.sms_app.presentation.screen  Icons 'com.example.sms_app.presentation.screen  Int 'com.example.sms_app.presentation.screen  IntentUtils 'com.example.sms_app.presentation.screen  
LazyColumn 'com.example.sms_app.presentation.screen  
MainScreen 'com.example.sms_app.presentation.screen  
MainViewModel 'com.example.sms_app.presentation.screen  Modifier 'com.example.sms_app.presentation.screen  Row 'com.example.sms_app.presentation.screen  Text 'com.example.sms_app.presentation.screen  	TextAlign 'com.example.sms_app.presentation.screen  	TextStyle 'com.example.sms_app.presentation.screen  Toast 'com.example.sms_app.presentation.screen  UpdateViewModel 'com.example.sms_app.presentation.screen  all 'com.example.sms_app.presentation.screen  android 'com.example.sms_app.presentation.screen  androidx 'com.example.sms_app.presentation.screen  arrayOf 'com.example.sms_app.presentation.screen  	clickable 'com.example.sms_app.presentation.screen  colors 'com.example.sms_app.presentation.screen  com 'com.example.sms_app.presentation.screen  
component1 'com.example.sms_app.presentation.screen  
component2 'com.example.sms_app.presentation.screen  count 'com.example.sms_app.presentation.screen  fillMaxSize 'com.example.sms_app.presentation.screen  filter 'com.example.sms_app.presentation.screen  find 'com.example.sms_app.presentation.screen  first 'com.example.sms_app.presentation.screen  forEachIndexed 'com.example.sms_app.presentation.screen  groupBy 'com.example.sms_app.presentation.screen  
isNotEmpty 'com.example.sms_app.presentation.screen  let 'com.example.sms_app.presentation.screen  listOf 'com.example.sms_app.presentation.screen  	lowercase 'com.example.sms_app.presentation.screen  map 'com.example.sms_app.presentation.screen  offset 'com.example.sms_app.presentation.screen  openFacebook 'com.example.sms_app.presentation.screen  openZalo 'com.example.sms_app.presentation.screen  padding 'com.example.sms_app.presentation.screen  provideDelegate 'com.example.sms_app.presentation.screen  size 'com.example.sms_app.presentation.screen  	uppercase 'com.example.sms_app.presentation.screen  weight 'com.example.sms_app.presentation.screen  example +com.example.sms_app.presentation.screen.com  sms_app 3com.example.sms_app.presentation.screen.com.example  data ;com.example.sms_app.presentation.screen.com.example.sms_app  Customer @com.example.sms_app.presentation.screen.com.example.sms_app.data  Boolean &com.example.sms_app.presentation.theme  Build &com.example.sms_app.presentation.theme  
Composable &com.example.sms_app.presentation.theme  DarkColorScheme &com.example.sms_app.presentation.theme  
FontFamily &com.example.sms_app.presentation.theme  
FontWeight &com.example.sms_app.presentation.theme  LightColorScheme &com.example.sms_app.presentation.theme  Pink40 &com.example.sms_app.presentation.theme  Pink80 &com.example.sms_app.presentation.theme  Purple40 &com.example.sms_app.presentation.theme  Purple80 &com.example.sms_app.presentation.theme  PurpleGrey40 &com.example.sms_app.presentation.theme  PurpleGrey80 &com.example.sms_app.presentation.theme  SmsAppTheme &com.example.sms_app.presentation.theme  
Typography &com.example.sms_app.presentation.theme  Unit &com.example.sms_app.presentation.theme  AddCustomerViewModel *com.example.sms_app.presentation.viewmodel  AndroidViewModel *com.example.sms_app.presentation.viewmodel  AppSettings *com.example.sms_app.presentation.viewmodel  AppUpdateManager *com.example.sms_app.presentation.viewmodel  Application *com.example.sms_app.presentation.viewmodel  BackUpViewModel *com.example.sms_app.presentation.viewmodel  Boolean *com.example.sms_app.presentation.viewmodel  BroadcastReceiver *com.example.sms_app.presentation.viewmodel  Build *com.example.sms_app.presentation.viewmodel  Context *com.example.sms_app.presentation.viewmodel  
ContextCompat *com.example.sms_app.presentation.viewmodel  CountDownTimer *com.example.sms_app.presentation.viewmodel  Customer *com.example.sms_app.presentation.viewmodel  
CustomerField *com.example.sms_app.presentation.viewmodel  Date *com.example.sms_app.presentation.viewmodel  Dispatchers *com.example.sms_app.presentation.viewmodel  Environment *com.example.sms_app.presentation.viewmodel  
ExcelImporter *com.example.sms_app.presentation.viewmodel  	Exception *com.example.sms_app.presentation.viewmodel  Files *com.example.sms_app.presentation.viewmodel  Float *com.example.sms_app.presentation.viewmodel  
HiltViewModel *com.example.sms_app.presentation.viewmodel  Inject *com.example.sms_app.presentation.viewmodel  Int *com.example.sms_app.presentation.viewmodel  Intent *com.example.sms_app.presentation.viewmodel  IntentFilter *com.example.sms_app.presentation.viewmodel  Job *com.example.sms_app.presentation.viewmodel  List *com.example.sms_app.presentation.viewmodel  LiveData *com.example.sms_app.presentation.viewmodel  Log *com.example.sms_app.presentation.viewmodel  Long *com.example.sms_app.presentation.viewmodel  
MainViewModel *com.example.sms_app.presentation.viewmodel  MessageTemplate *com.example.sms_app.presentation.viewmodel  MutableLiveData *com.example.sms_app.presentation.viewmodel  MutableStateFlow *com.example.sms_app.presentation.viewmodel  PatternViewModel *com.example.sms_app.presentation.viewmodel  SendMessageViewModel *com.example.sms_app.presentation.viewmodel  
SessionBackup *com.example.sms_app.presentation.viewmodel  SettingViewModel *com.example.sms_app.presentation.viewmodel  	SimConfig *com.example.sms_app.presentation.viewmodel  SimpleDateFormat *com.example.sms_app.presentation.viewmodel  SmsProgress *com.example.sms_app.presentation.viewmodel  
SmsRepository *com.example.sms_app.presentation.viewmodel  
SmsService *com.example.sms_app.presentation.viewmodel  	StateFlow *com.example.sms_app.presentation.viewmodel  String *com.example.sms_app.presentation.viewmodel  
StringBuilder *com.example.sms_app.presentation.viewmodel  
SwitchSetting *com.example.sms_app.presentation.viewmodel  System *com.example.sms_app.presentation.viewmodel  TAG *com.example.sms_app.presentation.viewmodel  Timber *com.example.sms_app.presentation.viewmodel  Unit *com.example.sms_app.presentation.viewmodel  UpdateViewModel *com.example.sms_app.presentation.viewmodel  Uri *com.example.sms_app.presentation.viewmodel  	ViewModel *com.example.sms_app.presentation.viewmodel  _appSettings *com.example.sms_app.presentation.viewmodel  _completion *com.example.sms_app.presentation.viewmodel  
_customers *com.example.sms_app.presentation.viewmodel  _default *com.example.sms_app.presentation.viewmodel  _downloadProgress *com.example.sms_app.presentation.viewmodel  _isDownloading *com.example.sms_app.presentation.viewmodel  
_isSending *com.example.sms_app.presentation.viewmodel  _messageTemplate *com.example.sms_app.presentation.viewmodel  _millisUntilFinished *com.example.sms_app.presentation.viewmodel  	_progress *com.example.sms_app.presentation.viewmodel  _selectedSim *com.example.sms_app.presentation.viewmodel  _showUpdateDialog *com.example.sms_app.presentation.viewmodel  _updateInfo *com.example.sms_app.presentation.viewmodel  also *com.example.sms_app.presentation.viewmodel  android *com.example.sms_app.presentation.viewmodel  appUpdateManager *com.example.sms_app.presentation.viewmodel  apply *com.example.sms_app.presentation.viewmodel  asStateFlow *com.example.sms_app.presentation.viewmodel  com *com.example.sms_app.presentation.viewmodel  
component1 *com.example.sms_app.presentation.viewmodel  
component2 *com.example.sms_app.presentation.viewmodel  contains *com.example.sms_app.presentation.viewmodel  count *com.example.sms_app.presentation.viewmodel  currentCountDownTimer *com.example.sms_app.presentation.viewmodel  d *com.example.sms_app.presentation.viewmodel  e *com.example.sms_app.presentation.viewmodel  edit *com.example.sms_app.presentation.viewmodel  filter *com.example.sms_app.presentation.viewmodel  first *com.example.sms_app.presentation.viewmodel  forEach *com.example.sms_app.presentation.viewmodel  forEachIndexed *com.example.sms_app.presentation.viewmodel  getApplication *com.example.sms_app.presentation.viewmodel  getValue *com.example.sms_app.presentation.viewmodel  groupBy *com.example.sms_app.presentation.viewmodel  isBlank *com.example.sms_app.presentation.viewmodel  isEmpty *com.example.sms_app.presentation.viewmodel  
isNotEmpty *com.example.sms_app.presentation.viewmodel  isValidPhoneNumber *com.example.sms_app.presentation.viewmodel  java *com.example.sms_app.presentation.viewmodel  kotlinx *com.example.sms_app.presentation.viewmodel  launch *com.example.sms_app.presentation.viewmodel  let *com.example.sms_app.presentation.viewmodel  	lowercase *com.example.sms_app.presentation.viewmodel  map *com.example.sms_app.presentation.viewmodel  maxOf *com.example.sms_app.presentation.viewmodel  	onFailure *com.example.sms_app.presentation.viewmodel  plus *com.example.sms_app.presentation.viewmodel  runCatching *com.example.sms_app.presentation.viewmodel  
smsRepository *com.example.sms_app.presentation.viewmodel  sortedBy *com.example.sms_app.presentation.viewmodel  startCountdownTimer *com.example.sms_app.presentation.viewmodel  sync *com.example.sms_app.presentation.viewmodel  tag *com.example.sms_app.presentation.viewmodel  take *com.example.sms_app.presentation.viewmodel  takeIf *com.example.sms_app.presentation.viewmodel  toByteArray *com.example.sms_app.presentation.viewmodel  toIntOrNull *com.example.sms_app.presentation.viewmodel  
toMutableList *com.example.sms_app.presentation.viewmodel  totalCustomers *com.example.sms_app.presentation.viewmodel  trim *com.example.sms_app.presentation.viewmodel  validateAndFormatPhoneNumber *com.example.sms_app.presentation.viewmodel  viewModelScope *com.example.sms_app.presentation.viewmodel  w *com.example.sms_app.presentation.viewmodel  withContext *com.example.sms_app.presentation.viewmodel  Customer ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  
CustomerField ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  Dispatchers ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  
SessionBackup ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  also ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  android ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  apply ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  getApplication ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  getValue ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  isValidPhoneNumber ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  java ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  launch ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  let ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  
smsRepository ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  takeIf ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  toIntOrNull ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  
toMutableList ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  trim ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  validateAndFormatPhoneNumber ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  verify ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  viewModelScope ?com.example.sms_app.presentation.viewmodel.AddCustomerViewModel  
UpdateInfo ;com.example.sms_app.presentation.viewmodel.AppUpdateManager  Date :com.example.sms_app.presentation.viewmodel.BackUpViewModel  Dispatchers :com.example.sms_app.presentation.viewmodel.BackUpViewModel  Environment :com.example.sms_app.presentation.viewmodel.BackUpViewModel  Files :com.example.sms_app.presentation.viewmodel.BackUpViewModel  SimpleDateFormat :com.example.sms_app.presentation.viewmodel.BackUpViewModel  
StringBuilder :com.example.sms_app.presentation.viewmodel.BackUpViewModel  backUp :com.example.sms_app.presentation.viewmodel.BackUpViewModel  isEmpty :com.example.sms_app.presentation.viewmodel.BackUpViewModel  java :com.example.sms_app.presentation.viewmodel.BackUpViewModel  launch :com.example.sms_app.presentation.viewmodel.BackUpViewModel  	onFailure :com.example.sms_app.presentation.viewmodel.BackUpViewModel  runCatching :com.example.sms_app.presentation.viewmodel.BackUpViewModel  
smsRepository :com.example.sms_app.presentation.viewmodel.BackUpViewModel  toByteArray :com.example.sms_app.presentation.viewmodel.BackUpViewModel  viewModelScope :com.example.sms_app.presentation.viewmodel.BackUpViewModel  Build 8com.example.sms_app.presentation.viewmodel.MainViewModel  Context 8com.example.sms_app.presentation.viewmodel.MainViewModel  Dispatchers 8com.example.sms_app.presentation.viewmodel.MainViewModel  
ExcelImporter 8com.example.sms_app.presentation.viewmodel.MainViewModel  IntentFilter 8com.example.sms_app.presentation.viewmodel.MainViewModel  MutableLiveData 8com.example.sms_app.presentation.viewmodel.MainViewModel  
SmsService 8com.example.sms_app.presentation.viewmodel.MainViewModel  
_customers 8com.example.sms_app.presentation.viewmodel.MainViewModel  android 8com.example.sms_app.presentation.viewmodel.MainViewModel  apply 8com.example.sms_app.presentation.viewmodel.MainViewModel  com 8com.example.sms_app.presentation.viewmodel.MainViewModel  
component1 8com.example.sms_app.presentation.viewmodel.MainViewModel  
component2 8com.example.sms_app.presentation.viewmodel.MainViewModel  contains 8com.example.sms_app.presentation.viewmodel.MainViewModel  count 8com.example.sms_app.presentation.viewmodel.MainViewModel  	customers 8com.example.sms_app.presentation.viewmodel.MainViewModel  delete 8com.example.sms_app.presentation.viewmodel.MainViewModel  	deleteAll 8com.example.sms_app.presentation.viewmodel.MainViewModel  filter 8com.example.sms_app.presentation.viewmodel.MainViewModel  first 8com.example.sms_app.presentation.viewmodel.MainViewModel  getApplication 8com.example.sms_app.presentation.viewmodel.MainViewModel  getDefaultTemplate 8com.example.sms_app.presentation.viewmodel.MainViewModel  getMessageTemplates 8com.example.sms_app.presentation.viewmodel.MainViewModel  groupBy 8com.example.sms_app.presentation.viewmodel.MainViewModel  handleExcelFile 8com.example.sms_app.presentation.viewmodel.MainViewModel  isBlank 8com.example.sms_app.presentation.viewmodel.MainViewModel  
isNotEmpty 8com.example.sms_app.presentation.viewmodel.MainViewModel  kotlinx 8com.example.sms_app.presentation.viewmodel.MainViewModel  launch 8com.example.sms_app.presentation.viewmodel.MainViewModel  let 8com.example.sms_app.presentation.viewmodel.MainViewModel  	lowercase 8com.example.sms_app.presentation.viewmodel.MainViewModel  map 8com.example.sms_app.presentation.viewmodel.MainViewModel  	onFailure 8com.example.sms_app.presentation.viewmodel.MainViewModel  plus 8com.example.sms_app.presentation.viewmodel.MainViewModel  runCatching 8com.example.sms_app.presentation.viewmodel.MainViewModel  searchCustomers 8com.example.sms_app.presentation.viewmodel.MainViewModel  	selectAll 8com.example.sms_app.presentation.viewmodel.MainViewModel  smsProgressReceiver 8com.example.sms_app.presentation.viewmodel.MainViewModel  
smsRepository 8com.example.sms_app.presentation.viewmodel.MainViewModel  sortedBy 8com.example.sms_app.presentation.viewmodel.MainViewModel  sync 8com.example.sms_app.presentation.viewmodel.MainViewModel  
toMutableList 8com.example.sms_app.presentation.viewmodel.MainViewModel  unselectAll 8com.example.sms_app.presentation.viewmodel.MainViewModel  updateCustomers 8com.example.sms_app.presentation.viewmodel.MainViewModel  viewModelScope 8com.example.sms_app.presentation.viewmodel.MainViewModel  Dispatchers ;com.example.sms_app.presentation.viewmodel.PatternViewModel  MessageTemplate ;com.example.sms_app.presentation.viewmodel.PatternViewModel  MutableLiveData ;com.example.sms_app.presentation.viewmodel.PatternViewModel  _default ;com.example.sms_app.presentation.viewmodel.PatternViewModel  _messageTemplate ;com.example.sms_app.presentation.viewmodel.PatternViewModel  _selectedSim ;com.example.sms_app.presentation.viewmodel.PatternViewModel  apply ;com.example.sms_app.presentation.viewmodel.PatternViewModel  default ;com.example.sms_app.presentation.viewmodel.PatternViewModel  filter ;com.example.sms_app.presentation.viewmodel.PatternViewModel  launch ;com.example.sms_app.presentation.viewmodel.PatternViewModel  messageTemplate ;com.example.sms_app.presentation.viewmodel.PatternViewModel  saveTemplate ;com.example.sms_app.presentation.viewmodel.PatternViewModel  selectedSim ;com.example.sms_app.presentation.viewmodel.PatternViewModel  
smsRepository ;com.example.sms_app.presentation.viewmodel.PatternViewModel  sortedBy ;com.example.sms_app.presentation.viewmodel.PatternViewModel  sync ;com.example.sms_app.presentation.viewmodel.PatternViewModel  
toMutableList ;com.example.sms_app.presentation.viewmodel.PatternViewModel  viewModelScope ;com.example.sms_app.presentation.viewmodel.PatternViewModel  Application ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Boolean ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  BroadcastReceiver ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Build ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Context ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
ContextCompat ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  CountDownTimer ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Dispatchers ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  	Exception ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Inject ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Int ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Intent ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  IntentFilter ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  LiveData ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Log ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Long ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  MessageTemplate ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  MutableLiveData ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
SessionBackup ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  	SimConfig ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  SmsProgress ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
SmsRepository ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
SmsService ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  String ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  System ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  TAG ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Timber ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  _completion ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
_isSending ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  _millisUntilFinished ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  	_progress ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  android ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  apply ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
completion ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  currentCountDownTimer ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  filter ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  forEachIndexed ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  getApplication ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  isBlank ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  	isSending ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  java ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  kotlinx ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  launch ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  let ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  map ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  maxOf ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  millisUntilFinished ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  progress ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel   restoreCountdownIfServiceRunning ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  sendMessage ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  smsProgressReceiver ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  
smsRepository ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  startCountdownTimer ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  stop ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  tag ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  take ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  totalCustomers ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  viewModelScope ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  withContext ?com.example.sms_app.presentation.viewmodel.SendMessageViewModel  Build Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Context Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  
ContextCompat Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Dispatchers Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Intent Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  IntentFilter Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Log Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  MutableLiveData Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  
SessionBackup Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  SmsProgress Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  
SmsService Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  System Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  TAG Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Timber Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  _completion Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  
_isSending Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  _millisUntilFinished Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  	_progress Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  android Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  apply Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  currentCountDownTimer Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  filter Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  forEachIndexed Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  getApplication Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  isBlank Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  java Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  kotlinx Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  launch Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  let Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  map Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  maxOf Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  
smsRepository Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  startCountdownTimer Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  tag Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  take Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  totalCustomers Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  viewModelScope Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  withContext Icom.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion  Dispatchers ;com.example.sms_app.presentation.viewmodel.SettingViewModel  MutableLiveData ;com.example.sms_app.presentation.viewmodel.SettingViewModel  
SwitchSetting ;com.example.sms_app.presentation.viewmodel.SettingViewModel  _appSettings ;com.example.sms_app.presentation.viewmodel.SettingViewModel  appSettings ;com.example.sms_app.presentation.viewmodel.SettingViewModel  apply ;com.example.sms_app.presentation.viewmodel.SettingViewModel  com ;com.example.sms_app.presentation.viewmodel.SettingViewModel  edit ;com.example.sms_app.presentation.viewmodel.SettingViewModel  launch ;com.example.sms_app.presentation.viewmodel.SettingViewModel  let ;com.example.sms_app.presentation.viewmodel.SettingViewModel  saveBool ;com.example.sms_app.presentation.viewmodel.SettingViewModel  saveCustomerLimit ;com.example.sms_app.presentation.viewmodel.SettingViewModel  	saveDelay ;com.example.sms_app.presentation.viewmodel.SettingViewModel  	saveRetry ;com.example.sms_app.presentation.viewmodel.SettingViewModel  
smsRepository ;com.example.sms_app.presentation.viewmodel.SettingViewModel  sync ;com.example.sms_app.presentation.viewmodel.SettingViewModel  toIntOrNull ;com.example.sms_app.presentation.viewmodel.SettingViewModel  viewModelScope ;com.example.sms_app.presentation.viewmodel.SettingViewModel  MutableStateFlow :com.example.sms_app.presentation.viewmodel.UpdateViewModel  Timber :com.example.sms_app.presentation.viewmodel.UpdateViewModel  _downloadProgress :com.example.sms_app.presentation.viewmodel.UpdateViewModel  _isDownloading :com.example.sms_app.presentation.viewmodel.UpdateViewModel  _showUpdateDialog :com.example.sms_app.presentation.viewmodel.UpdateViewModel  _updateInfo :com.example.sms_app.presentation.viewmodel.UpdateViewModel  appUpdateManager :com.example.sms_app.presentation.viewmodel.UpdateViewModel  asStateFlow :com.example.sms_app.presentation.viewmodel.UpdateViewModel  checkForUpdates :com.example.sms_app.presentation.viewmodel.UpdateViewModel  d :com.example.sms_app.presentation.viewmodel.UpdateViewModel  dismissUpdateDialog :com.example.sms_app.presentation.viewmodel.UpdateViewModel  downloadProgress :com.example.sms_app.presentation.viewmodel.UpdateViewModel  e :com.example.sms_app.presentation.viewmodel.UpdateViewModel  getCurrentVersion :com.example.sms_app.presentation.viewmodel.UpdateViewModel  
isDownloading :com.example.sms_app.presentation.viewmodel.UpdateViewModel  launch :com.example.sms_app.presentation.viewmodel.UpdateViewModel  showUpdateDialog :com.example.sms_app.presentation.viewmodel.UpdateViewModel  startUpdate :com.example.sms_app.presentation.viewmodel.UpdateViewModel  
updateInfo :com.example.sms_app.presentation.viewmodel.UpdateViewModel  viewModelScope :com.example.sms_app.presentation.viewmodel.UpdateViewModel  w :com.example.sms_app.presentation.viewmodel.UpdateViewModel  example .com.example.sms_app.presentation.viewmodel.com  sms_app 6com.example.sms_app.presentation.viewmodel.com.example  presentation >com.example.sms_app.presentation.viewmodel.com.example.sms_app  	component Kcom.example.sms_app.presentation.viewmodel.com.example.sms_app.presentation  
NumSetting Ucom.example.sms_app.presentation.viewmodel.com.example.sms_app.presentation.component  ACTION_CUSTOMER_DELETED com.example.sms_app.service  ACTION_PROGRESS_UPDATE com.example.sms_app.service  ACTION_SMS_COMPLETED com.example.sms_app.service  ACTION_SMS_COUNT_UPDATED com.example.sms_app.service  Activity com.example.sms_app.service  AndroidEntryPoint com.example.sms_app.service  	ArrayList com.example.sms_app.service  Boolean com.example.sms_app.service  BroadcastReceiver com.example.sms_app.service  Build com.example.sms_app.service  
CHANNEL_ID com.example.sms_app.service  CancellableContinuation com.example.sms_app.service  CancellationException com.example.sms_app.service  Context com.example.sms_app.service  CoroutineScope com.example.sms_app.service  Customer com.example.sms_app.service  Dispatchers com.example.sms_app.service  ERROR_RETRY_FAILED com.example.sms_app.service  EXTRA_CUSTOMER_ID com.example.sms_app.service  EXTRA_INTERVAL_SECONDS com.example.sms_app.service  EXTRA_MAX_RETRY com.example.sms_app.service  
EXTRA_MESSAGE com.example.sms_app.service  EXTRA_PROGRESS com.example.sms_app.service  EXTRA_RETRY_DELAY com.example.sms_app.service  EXTRA_SIM_ID com.example.sms_app.service  EXTRA_SMS_COUNT com.example.sms_app.service  EXTRA_TEMPLATE_ID com.example.sms_app.service  EXTRA_TOTAL com.example.sms_app.service  	Exception com.example.sms_app.service  Handler com.example.sms_app.service  HiddenSmsReceiver com.example.sms_app.service  IBinder com.example.sms_app.service  IllegalStateException com.example.sms_app.service  Int com.example.sms_app.service  Intent com.example.sms_app.service  IntentFilter com.example.sms_app.service  Job com.example.sms_app.service  List com.example.sms_app.service  Log com.example.sms_app.service  Long com.example.sms_app.service  Looper com.example.sms_app.service  Manifest com.example.sms_app.service  MutableList com.example.sms_app.service  
MutableSet com.example.sms_app.service  NOTIFICATION_CHANNEL_ID com.example.sms_app.service  NOTIFICATION_ID com.example.sms_app.service  Notification com.example.sms_app.service  NotificationChannel com.example.sms_app.service  NotificationCompat com.example.sms_app.service  NotificationManager com.example.sms_app.service  Pair com.example.sms_app.service  
PendingIntent com.example.sms_app.service  Regex com.example.sms_app.service  RequiresPermission com.example.sms_app.service  SMS_DELIVERED_ACTION com.example.sms_app.service  SMS_SENT_ACTION com.example.sms_app.service  START_NOT_STICKY com.example.sms_app.service  START_REDELIVER_INTENT com.example.sms_app.service  SecurityException com.example.sms_app.service  Service com.example.sms_app.service  Settings com.example.sms_app.service  
SmsAttempt com.example.sms_app.service  
SmsManager com.example.sms_app.service  
SmsRepository com.example.sms_app.service  
SmsService com.example.sms_app.service  SmsTemplate com.example.sms_app.service  String com.example.sms_app.service  SubscriptionManager com.example.sms_app.service  SuppressLint com.example.sms_app.service  System com.example.sms_app.service  TAG com.example.sms_app.service  TELEPHONY_SERVICE com.example.sms_app.service  TelephonyManager com.example.sms_app.service  activeAttempts com.example.sms_app.service  android com.example.sms_app.service  any com.example.sms_app.service  apply com.example.sms_app.service  arrayOf com.example.sms_app.service  async com.example.sms_app.service  attemptQueue com.example.sms_app.service  coerceIn com.example.sms_app.service  com com.example.sms_app.service  contains com.example.sms_app.service  coroutineScope com.example.sms_app.service  delay com.example.sms_app.service  distinct com.example.sms_app.service  endsWith com.example.sms_app.service  executeNextAttempt com.example.sms_app.service  filter com.example.sms_app.service  find com.example.sms_app.service  forEach com.example.sms_app.service  format com.example.sms_app.service  getRandomDelay com.example.sms_app.service  getSystemService com.example.sms_app.service  handleSendFailure com.example.sms_app.service  hasRequiredPermissions com.example.sms_app.service  indices com.example.sms_app.service  
initialize com.example.sms_app.service  
isNotEmpty com.example.sms_app.service  
isNullOrEmpty com.example.sms_app.service  	isRunning com.example.sms_app.service  java com.example.sms_app.service  joinToString com.example.sms_app.service  kotlinx com.example.sms_app.service  launch com.example.sms_app.service  let com.example.sms_app.service  listOf com.example.sms_app.service  longArrayOf com.example.sms_app.service  map com.example.sms_app.service  maxOf com.example.sms_app.service  minOf com.example.sms_app.service  minusAssign com.example.sms_app.service  
mutableListOf com.example.sms_app.service  mutableMapOf com.example.sms_app.service  pendingSmsDeliveries com.example.sms_app.service  pendingSmsResults com.example.sms_app.service  
plusAssign com.example.sms_app.service  random com.example.sms_app.service  replace com.example.sms_app.service  resume com.example.sms_app.service  sendCompletionBroadcast com.example.sms_app.service  sendProgressBroadcast com.example.sms_app.service  sendSmsToCustomers com.example.sms_app.service  sendSmsWithDeliveryReport com.example.sms_app.service  set com.example.sms_app.service  startSendingSms com.example.sms_app.service  
startsWith com.example.sms_app.service  stopSelf com.example.sms_app.service  	substring com.example.sms_app.service  suspendCancellableCoroutine com.example.sms_app.service  take com.example.sms_app.service  to com.example.sms_app.service  	totalSent com.example.sms_app.service  totalToSend com.example.sms_app.service  trim com.example.sms_app.service  	withIndex com.example.sms_app.service  Build -com.example.sms_app.service.HiddenSmsReceiver  Intent -com.example.sms_app.service.HiddenSmsReceiver  Log -com.example.sms_app.service.HiddenSmsReceiver  
SmsRepository -com.example.sms_app.service.HiddenSmsReceiver  
SmsService -com.example.sms_app.service.HiddenSmsReceiver  TAG -com.example.sms_app.service.HiddenSmsReceiver  java -com.example.sms_app.service.HiddenSmsReceiver  ACTION_CUSTOMER_DELETED &com.example.sms_app.service.SmsService  ACTION_PROGRESS_UPDATE &com.example.sms_app.service.SmsService  ACTION_SMS_COMPLETED &com.example.sms_app.service.SmsService  ACTION_SMS_COUNT_UPDATED &com.example.sms_app.service.SmsService  Activity &com.example.sms_app.service.SmsService  	ArrayList &com.example.sms_app.service.SmsService  Boolean &com.example.sms_app.service.SmsService  BroadcastReceiver &com.example.sms_app.service.SmsService  Build &com.example.sms_app.service.SmsService  
CHANNEL_ID &com.example.sms_app.service.SmsService  CancellableContinuation &com.example.sms_app.service.SmsService  CancellationException &com.example.sms_app.service.SmsService  	Companion &com.example.sms_app.service.SmsService  Context &com.example.sms_app.service.SmsService  CoroutineScope &com.example.sms_app.service.SmsService  Customer &com.example.sms_app.service.SmsService  Dispatchers &com.example.sms_app.service.SmsService  ERROR_RETRY_FAILED &com.example.sms_app.service.SmsService  EXTRA_CUSTOMER_ID &com.example.sms_app.service.SmsService  EXTRA_INTERVAL_SECONDS &com.example.sms_app.service.SmsService  EXTRA_MAX_RETRY &com.example.sms_app.service.SmsService  
EXTRA_MESSAGE &com.example.sms_app.service.SmsService  EXTRA_PROGRESS &com.example.sms_app.service.SmsService  EXTRA_RETRY_DELAY &com.example.sms_app.service.SmsService  EXTRA_SIM_ID &com.example.sms_app.service.SmsService  EXTRA_SMS_COUNT &com.example.sms_app.service.SmsService  EXTRA_TEMPLATE_ID &com.example.sms_app.service.SmsService  EXTRA_TOTAL &com.example.sms_app.service.SmsService  	Exception &com.example.sms_app.service.SmsService  Handler &com.example.sms_app.service.SmsService  IBinder &com.example.sms_app.service.SmsService  IllegalStateException &com.example.sms_app.service.SmsService  Int &com.example.sms_app.service.SmsService  Intent &com.example.sms_app.service.SmsService  IntentFilter &com.example.sms_app.service.SmsService  Job &com.example.sms_app.service.SmsService  List &com.example.sms_app.service.SmsService  Log &com.example.sms_app.service.SmsService  Long &com.example.sms_app.service.SmsService  Looper &com.example.sms_app.service.SmsService  Manifest &com.example.sms_app.service.SmsService  MutableList &com.example.sms_app.service.SmsService  
MutableSet &com.example.sms_app.service.SmsService  NOTIFICATION_CHANNEL_ID &com.example.sms_app.service.SmsService  NOTIFICATION_ID &com.example.sms_app.service.SmsService  Notification &com.example.sms_app.service.SmsService  NotificationChannel &com.example.sms_app.service.SmsService  NotificationCompat &com.example.sms_app.service.SmsService  NotificationManager &com.example.sms_app.service.SmsService  Pair &com.example.sms_app.service.SmsService  
PendingIntent &com.example.sms_app.service.SmsService  Regex &com.example.sms_app.service.SmsService  RequiresPermission &com.example.sms_app.service.SmsService  SMS_DELIVERED_ACTION &com.example.sms_app.service.SmsService  SMS_SENT_ACTION &com.example.sms_app.service.SmsService  START_NOT_STICKY &com.example.sms_app.service.SmsService  START_REDELIVER_INTENT &com.example.sms_app.service.SmsService  SecurityException &com.example.sms_app.service.SmsService  Settings &com.example.sms_app.service.SmsService  
SmsAttempt &com.example.sms_app.service.SmsService  
SmsManager &com.example.sms_app.service.SmsService  
SmsRepository &com.example.sms_app.service.SmsService  SmsTemplate &com.example.sms_app.service.SmsService  String &com.example.sms_app.service.SmsService  SubscriptionManager &com.example.sms_app.service.SmsService  SuppressLint &com.example.sms_app.service.SmsService  System &com.example.sms_app.service.SmsService  TAG &com.example.sms_app.service.SmsService  TELEPHONY_SERVICE &com.example.sms_app.service.SmsService  TelephonyManager &com.example.sms_app.service.SmsService  activeAttempts &com.example.sms_app.service.SmsService  android &com.example.sms_app.service.SmsService  any &com.example.sms_app.service.SmsService  applicationContext &com.example.sms_app.service.SmsService  apply &com.example.sms_app.service.SmsService  arrayOf &com.example.sms_app.service.SmsService  async &com.example.sms_app.service.SmsService  attemptQueue &com.example.sms_app.service.SmsService  checkDeviceSettings &com.example.sms_app.service.SmsService  checkSimAndCustomers &com.example.sms_app.service.SmsService  coerceIn &com.example.sms_app.service.SmsService  com &com.example.sms_app.service.SmsService  contains &com.example.sms_app.service.SmsService  contentResolver &com.example.sms_app.service.SmsService  coroutineScope &com.example.sms_app.service.SmsService  createHiddenNotification &com.example.sms_app.service.SmsService  createNotificationChannel &com.example.sms_app.service.SmsService  currentProgress &com.example.sms_app.service.SmsService  currentTemplateId &com.example.sms_app.service.SmsService  delay &com.example.sms_app.service.SmsService  !deleteCustomerAfterSuccessfulSend &com.example.sms_app.service.SmsService  distinct &com.example.sms_app.service.SmsService  endsWith &com.example.sms_app.service.SmsService  executeNextAttempt &com.example.sms_app.service.SmsService  filter &com.example.sms_app.service.SmsService  find &com.example.sms_app.service.SmsService  format &com.example.sms_app.service.SmsService  
formatMessage &com.example.sms_app.service.SmsService  getRandomDelay &com.example.sms_app.service.SmsService  getSystemService &com.example.sms_app.service.SmsService  handleSendFailure &com.example.sms_app.service.SmsService  hasRequiredPermissions &com.example.sms_app.service.SmsService  indices &com.example.sms_app.service.SmsService  
initialize &com.example.sms_app.service.SmsService  intervalSeconds &com.example.sms_app.service.SmsService  
isNotEmpty &com.example.sms_app.service.SmsService  
isNullOrEmpty &com.example.sms_app.service.SmsService  	isRunning &com.example.sms_app.service.SmsService  isSendingMessages &com.example.sms_app.service.SmsService  java &com.example.sms_app.service.SmsService  joinToString &com.example.sms_app.service.SmsService  kotlinx &com.example.sms_app.service.SmsService  launch &com.example.sms_app.service.SmsService  let &com.example.sms_app.service.SmsService  listOf &com.example.sms_app.service.SmsService  longArrayOf &com.example.sms_app.service.SmsService  map &com.example.sms_app.service.SmsService  maxOf &com.example.sms_app.service.SmsService  maxRetryAttempts &com.example.sms_app.service.SmsService  minOf &com.example.sms_app.service.SmsService  minusAssign &com.example.sms_app.service.SmsService  multipartMessageTracker &com.example.sms_app.service.SmsService  
mutableListOf &com.example.sms_app.service.SmsService  mutableMapOf &com.example.sms_app.service.SmsService  pendingSmsDeliveries &com.example.sms_app.service.SmsService  pendingSmsResults &com.example.sms_app.service.SmsService  
plusAssign &com.example.sms_app.service.SmsService  random &com.example.sms_app.service.SmsService  registerReceiver &com.example.sms_app.service.SmsService  replace &com.example.sms_app.service.SmsService  resume &com.example.sms_app.service.SmsService  retryDelaySeconds &com.example.sms_app.service.SmsService  
sendBroadcast &com.example.sms_app.service.SmsService  sendCompletionBroadcast &com.example.sms_app.service.SmsService  sendProgressBroadcast &com.example.sms_app.service.SmsService  sendSmsCountUpdateBroadcast &com.example.sms_app.service.SmsService  sendSmsToCustomers &com.example.sms_app.service.SmsService  sendSmsWithDeliveryReport &com.example.sms_app.service.SmsService  
serviceJob &com.example.sms_app.service.SmsService  serviceScope &com.example.sms_app.service.SmsService  set &com.example.sms_app.service.SmsService  setupSmsDeliveryReceiver &com.example.sms_app.service.SmsService  setupSmsResultReceiver &com.example.sms_app.service.SmsService  smsDeliveryReceiver &com.example.sms_app.service.SmsService  
smsRepository &com.example.sms_app.service.SmsService  smsResultReceiver &com.example.sms_app.service.SmsService  startForeground &com.example.sms_app.service.SmsService  startSendingSms &com.example.sms_app.service.SmsService  
startsWith &com.example.sms_app.service.SmsService  stopSelf &com.example.sms_app.service.SmsService  	substring &com.example.sms_app.service.SmsService  suspendCancellableCoroutine &com.example.sms_app.service.SmsService  take &com.example.sms_app.service.SmsService  to &com.example.sms_app.service.SmsService  totalMessageCount &com.example.sms_app.service.SmsService  	totalSent &com.example.sms_app.service.SmsService  totalToSend &com.example.sms_app.service.SmsService  trim &com.example.sms_app.service.SmsService  unregisterReceiver &com.example.sms_app.service.SmsService  	withIndex &com.example.sms_app.service.SmsService  ACTION_CUSTOMER_DELETED 0com.example.sms_app.service.SmsService.Companion  ACTION_PROGRESS_UPDATE 0com.example.sms_app.service.SmsService.Companion  ACTION_SMS_COMPLETED 0com.example.sms_app.service.SmsService.Companion  ACTION_SMS_COUNT_UPDATED 0com.example.sms_app.service.SmsService.Companion  Activity 0com.example.sms_app.service.SmsService.Companion  	ArrayList 0com.example.sms_app.service.SmsService.Companion  Build 0com.example.sms_app.service.SmsService.Companion  
CHANNEL_ID 0com.example.sms_app.service.SmsService.Companion  Context 0com.example.sms_app.service.SmsService.Companion  CoroutineScope 0com.example.sms_app.service.SmsService.Companion  Dispatchers 0com.example.sms_app.service.SmsService.Companion  ERROR_RETRY_FAILED 0com.example.sms_app.service.SmsService.Companion  EXTRA_CUSTOMER_ID 0com.example.sms_app.service.SmsService.Companion  EXTRA_INTERVAL_SECONDS 0com.example.sms_app.service.SmsService.Companion  EXTRA_MAX_RETRY 0com.example.sms_app.service.SmsService.Companion  
EXTRA_MESSAGE 0com.example.sms_app.service.SmsService.Companion  EXTRA_PROGRESS 0com.example.sms_app.service.SmsService.Companion  EXTRA_RETRY_DELAY 0com.example.sms_app.service.SmsService.Companion  EXTRA_SIM_ID 0com.example.sms_app.service.SmsService.Companion  EXTRA_SMS_COUNT 0com.example.sms_app.service.SmsService.Companion  EXTRA_TEMPLATE_ID 0com.example.sms_app.service.SmsService.Companion  EXTRA_TOTAL 0com.example.sms_app.service.SmsService.Companion  Handler 0com.example.sms_app.service.SmsService.Companion  Intent 0com.example.sms_app.service.SmsService.Companion  IntentFilter 0com.example.sms_app.service.SmsService.Companion  Log 0com.example.sms_app.service.SmsService.Companion  Looper 0com.example.sms_app.service.SmsService.Companion  Manifest 0com.example.sms_app.service.SmsService.Companion  NOTIFICATION_CHANNEL_ID 0com.example.sms_app.service.SmsService.Companion  NOTIFICATION_ID 0com.example.sms_app.service.SmsService.Companion  Notification 0com.example.sms_app.service.SmsService.Companion  NotificationChannel 0com.example.sms_app.service.SmsService.Companion  NotificationCompat 0com.example.sms_app.service.SmsService.Companion  NotificationManager 0com.example.sms_app.service.SmsService.Companion  Pair 0com.example.sms_app.service.SmsService.Companion  
PendingIntent 0com.example.sms_app.service.SmsService.Companion  Regex 0com.example.sms_app.service.SmsService.Companion  SMS_DELIVERED_ACTION 0com.example.sms_app.service.SmsService.Companion  SMS_SENT_ACTION 0com.example.sms_app.service.SmsService.Companion  START_NOT_STICKY 0com.example.sms_app.service.SmsService.Companion  START_REDELIVER_INTENT 0com.example.sms_app.service.SmsService.Companion  Settings 0com.example.sms_app.service.SmsService.Companion  
SmsAttempt 0com.example.sms_app.service.SmsService.Companion  
SmsManager 0com.example.sms_app.service.SmsService.Companion  
SmsRepository 0com.example.sms_app.service.SmsService.Companion  SmsTemplate 0com.example.sms_app.service.SmsService.Companion  String 0com.example.sms_app.service.SmsService.Companion  System 0com.example.sms_app.service.SmsService.Companion  TAG 0com.example.sms_app.service.SmsService.Companion  TELEPHONY_SERVICE 0com.example.sms_app.service.SmsService.Companion  TelephonyManager 0com.example.sms_app.service.SmsService.Companion  activeAttempts 0com.example.sms_app.service.SmsService.Companion  android 0com.example.sms_app.service.SmsService.Companion  any 0com.example.sms_app.service.SmsService.Companion  apply 0com.example.sms_app.service.SmsService.Companion  arrayOf 0com.example.sms_app.service.SmsService.Companion  async 0com.example.sms_app.service.SmsService.Companion  attemptQueue 0com.example.sms_app.service.SmsService.Companion  coerceIn 0com.example.sms_app.service.SmsService.Companion  com 0com.example.sms_app.service.SmsService.Companion  contains 0com.example.sms_app.service.SmsService.Companion  coroutineScope 0com.example.sms_app.service.SmsService.Companion  delay 0com.example.sms_app.service.SmsService.Companion  distinct 0com.example.sms_app.service.SmsService.Companion  endsWith 0com.example.sms_app.service.SmsService.Companion  executeNextAttempt 0com.example.sms_app.service.SmsService.Companion  filter 0com.example.sms_app.service.SmsService.Companion  find 0com.example.sms_app.service.SmsService.Companion  format 0com.example.sms_app.service.SmsService.Companion  getRandomDelay 0com.example.sms_app.service.SmsService.Companion  getSystemService 0com.example.sms_app.service.SmsService.Companion  handleSendFailure 0com.example.sms_app.service.SmsService.Companion  hasRequiredPermissions 0com.example.sms_app.service.SmsService.Companion  indices 0com.example.sms_app.service.SmsService.Companion  
initialize 0com.example.sms_app.service.SmsService.Companion  
isNotEmpty 0com.example.sms_app.service.SmsService.Companion  
isNullOrEmpty 0com.example.sms_app.service.SmsService.Companion  	isRunning 0com.example.sms_app.service.SmsService.Companion  java 0com.example.sms_app.service.SmsService.Companion  joinToString 0com.example.sms_app.service.SmsService.Companion  launch 0com.example.sms_app.service.SmsService.Companion  let 0com.example.sms_app.service.SmsService.Companion  listOf 0com.example.sms_app.service.SmsService.Companion  longArrayOf 0com.example.sms_app.service.SmsService.Companion  map 0com.example.sms_app.service.SmsService.Companion  maxOf 0com.example.sms_app.service.SmsService.Companion  minOf 0com.example.sms_app.service.SmsService.Companion  minusAssign 0com.example.sms_app.service.SmsService.Companion  
mutableListOf 0com.example.sms_app.service.SmsService.Companion  mutableMapOf 0com.example.sms_app.service.SmsService.Companion  pendingSmsDeliveries 0com.example.sms_app.service.SmsService.Companion  pendingSmsResults 0com.example.sms_app.service.SmsService.Companion  
plusAssign 0com.example.sms_app.service.SmsService.Companion  random 0com.example.sms_app.service.SmsService.Companion  replace 0com.example.sms_app.service.SmsService.Companion  resume 0com.example.sms_app.service.SmsService.Companion  sendCompletionBroadcast 0com.example.sms_app.service.SmsService.Companion  sendProgressBroadcast 0com.example.sms_app.service.SmsService.Companion  sendSmsToCustomers 0com.example.sms_app.service.SmsService.Companion  sendSmsWithDeliveryReport 0com.example.sms_app.service.SmsService.Companion  set 0com.example.sms_app.service.SmsService.Companion  startSendingSms 0com.example.sms_app.service.SmsService.Companion  
startsWith 0com.example.sms_app.service.SmsService.Companion  stopSelf 0com.example.sms_app.service.SmsService.Companion  	substring 0com.example.sms_app.service.SmsService.Companion  suspendCancellableCoroutine 0com.example.sms_app.service.SmsService.Companion  take 0com.example.sms_app.service.SmsService.Companion  to 0com.example.sms_app.service.SmsService.Companion  	totalSent 0com.example.sms_app.service.SmsService.Companion  totalToSend 0com.example.sms_app.service.SmsService.Companion  trim 0com.example.sms_app.service.SmsService.Companion  	withIndex 0com.example.sms_app.service.SmsService.Companion  
attemptNumber 1com.example.sms_app.service.SmsService.SmsAttempt  customer 1com.example.sms_app.service.SmsService.SmsAttempt  managerIndex 1com.example.sms_app.service.SmsService.SmsAttempt  maxAttempts 1com.example.sms_app.service.SmsService.SmsAttempt  message 1com.example.sms_app.service.SmsService.SmsAttempt  phoneFormat 1com.example.sms_app.service.SmsService.SmsAttempt  
smsManager 1com.example.sms_app.service.SmsService.SmsAttempt  app .com.example.sms_app.service.SmsService.android  Notification 2com.example.sms_app.service.SmsService.android.app  
coroutines .com.example.sms_app.service.SmsService.kotlinx  CancellableContinuation 9com.example.sms_app.service.SmsService.kotlinx.coroutines  app #com.example.sms_app.service.android  Notification 'com.example.sms_app.service.android.app  
coroutines #com.example.sms_app.service.kotlinx  CancellableContinuation .com.example.sms_app.service.kotlinx.coroutines  AboutScreen com.example.sms_app.ui  
AccessTime com.example.sms_app.ui  AddCustomerDialog com.example.sms_app.ui  AlertDialog com.example.sms_app.ui  	Alignment com.example.sms_app.ui  AppSettings com.example.sms_app.ui  Arrangement com.example.sms_app.ui  Boolean com.example.sms_app.ui  Box com.example.sms_app.ui  Button com.example.sms_app.ui  ButtonDefaults com.example.sms_app.ui  Card com.example.sms_app.ui  CardDefaults com.example.sms_app.ui  CircleShape com.example.sms_app.ui  Close com.example.sms_app.ui  Color com.example.sms_app.ui  Column com.example.sms_app.ui  
Composable com.example.sms_app.ui  Customer com.example.sms_app.ui  CustomerDetailDialog com.example.sms_app.ui  
CustomerField com.example.sms_app.ui  Date com.example.sms_app.ui  Divider com.example.sms_app.ui  
FontWeight com.example.sms_app.ui  HorizontalDivider com.example.sms_app.ui  Icon com.example.sms_app.ui  
IconButton com.example.sms_app.ui  Icons com.example.sms_app.ui  ImageVector com.example.sms_app.ui  Int com.example.sms_app.ui  LaunchedEffect com.example.sms_app.ui  
LazyColumn com.example.sms_app.ui  List com.example.sms_app.ui  Locale com.example.sms_app.ui  Log com.example.sms_app.ui  
MaterialTheme com.example.sms_app.ui  MessageTemplate com.example.sms_app.ui  Modifier com.example.sms_app.ui  OutlinedButton com.example.sms_app.ui  OutlinedTextField com.example.sms_app.ui  OutlinedTextFieldDefaults com.example.sms_app.ui  
PaddingValues com.example.sms_app.ui  RadioButton com.example.sms_app.ui  RadioButtonDefaults com.example.sms_app.ui  Refresh com.example.sms_app.ui  RemoveDuplicatesConfirmDialog com.example.sms_app.ui  RestoreUnsentCustomersDialog com.example.sms_app.ui  RoundedCornerShape com.example.sms_app.ui  Row com.example.sms_app.ui  Save com.example.sms_app.ui  Schedule com.example.sms_app.ui  SettingItem com.example.sms_app.ui  SettingSection com.example.sms_app.ui  SettingsDialog com.example.sms_app.ui  SimCard com.example.sms_app.ui  
SimManager com.example.sms_app.ui  SmsPreviewDialog com.example.sms_app.ui  
SmsRepository com.example.sms_app.ui  
SmsSession com.example.sms_app.ui  Spacer com.example.sms_app.ui  String com.example.sms_app.ui  TemplateConfigDialog com.example.sms_app.ui  TemplateManager com.example.sms_app.ui  TemplateSelectionDialog com.example.sms_app.ui  TemplateSelectionItem com.example.sms_app.ui  Text com.example.sms_app.ui  	TextAlign com.example.sms_app.ui  
TextButton com.example.sms_app.ui  Timer com.example.sms_app.ui  Toast com.example.sms_app.ui  UUID com.example.sms_app.ui  Unit com.example.sms_app.ui  align com.example.sms_app.ui  android com.example.sms_app.ui  androidx com.example.sms_app.ui  any com.example.sms_app.ui  
background com.example.sms_app.ui  buttonColors com.example.sms_app.ui  
cardColors com.example.sms_app.ui  
cardElevation com.example.sms_app.ui  	clickable com.example.sms_app.ui  colors com.example.sms_app.ui  com com.example.sms_app.ui  contains com.example.sms_app.ui  delay com.example.sms_app.ui  
fillMaxHeight com.example.sms_app.ui  fillMaxSize com.example.sms_app.ui  fillMaxWidth com.example.sms_app.ui  filter com.example.sms_app.ui  find com.example.sms_app.ui  first com.example.sms_app.ui  forEach com.example.sms_app.ui  getAvailableSims com.example.sms_app.ui  getDefaultTemplates com.example.sms_app.ui  getValue com.example.sms_app.ui  height com.example.sms_app.ui  heightIn com.example.sms_app.ui  isEmpty com.example.sms_app.ui  
isNotEmpty com.example.sms_app.ui  isValidPhoneNumber com.example.sms_app.ui  map com.example.sms_app.ui  mutableStateOf com.example.sms_app.ui  outlinedButtonColors com.example.sms_app.ui  padding com.example.sms_app.ui  provideDelegate com.example.sms_app.ui  remember com.example.sms_app.ui  rememberScrollState com.example.sms_app.ui  replace com.example.sms_app.ui  setValue com.example.sms_app.ui  size com.example.sms_app.ui  spacedBy com.example.sms_app.ui  take com.example.sms_app.ui  takeIf com.example.sms_app.ui  toInt com.example.sms_app.ui  toIntOrNull com.example.sms_app.ui  trim com.example.sms_app.ui  validateAndFormatPhoneNumber com.example.sms_app.ui  verticalScroll com.example.sms_app.ui  weight com.example.sms_app.ui  width com.example.sms_app.ui  compose com.example.sms_app.ui.androidx  ui 'com.example.sms_app.ui.androidx.compose  graphics *com.example.sms_app.ui.androidx.compose.ui  vector 3com.example.sms_app.ui.androidx.compose.ui.graphics  ImageVector :com.example.sms_app.ui.androidx.compose.ui.graphics.vector  AboutScreen !com.example.sms_app.ui.components  	Alignment !com.example.sms_app.ui.components  
AppLogoScreen !com.example.sms_app.ui.components  Arrangement !com.example.sms_app.ui.components  Box !com.example.sms_app.ui.components  Card !com.example.sms_app.ui.components  CardDefaults !com.example.sms_app.ui.components  CircularProgressIndicator !com.example.sms_app.ui.components  Color !com.example.sms_app.ui.components  Column !com.example.sms_app.ui.components  
Composable !com.example.sms_app.ui.components  
FontWeight !com.example.sms_app.ui.components  Icon !com.example.sms_app.ui.components  Icons !com.example.sms_app.ui.components  Image !com.example.sms_app.ui.components  LaunchedEffect !com.example.sms_app.ui.components  Long !com.example.sms_app.ui.components  Modifier !com.example.sms_app.ui.components  R !com.example.sms_app.ui.components  RoundedCornerShape !com.example.sms_app.ui.components  Spacer !com.example.sms_app.ui.components  SplashScreen !com.example.sms_app.ui.components  Text !com.example.sms_app.ui.components  	TextAlign !com.example.sms_app.ui.components  Unit !com.example.sms_app.ui.components  
cardColors !com.example.sms_app.ui.components  
cardElevation !com.example.sms_app.ui.components  delay !com.example.sms_app.ui.components  fillMaxSize !com.example.sms_app.ui.components  fillMaxWidth !com.example.sms_app.ui.components  height !com.example.sms_app.ui.components  padding !com.example.sms_app.ui.components  painterResource !com.example.sms_app.ui.components  size !com.example.sms_app.ui.components  ACTION_CUSTOMER_DELETED com.example.sms_app.utils  ACTION_PROGRESS_UPDATE com.example.sms_app.utils  ACTION_SMS_COMPLETED com.example.sms_app.utils  ACTION_SMS_COUNT_UPDATED com.example.sms_app.utils  Activity com.example.sms_app.utils  AndroidEntryPoint com.example.sms_app.utils  Any com.example.sms_app.utils  AppUpdateManager com.example.sms_app.utils  	ArrayList com.example.sms_app.utils  AutoSmsDisabler com.example.sms_app.utils  BASE_URL com.example.sms_app.utils  Base64 com.example.sms_app.utils  Boolean com.example.sms_app.utils  BroadcastReceiver com.example.sms_app.utils  Build com.example.sms_app.utils  BuildConfig com.example.sms_app.utils  	ByteArray com.example.sms_app.utils  
CHANNEL_ID com.example.sms_app.utils  CURRENT_VERSION com.example.sms_app.utils  CURRENT_VERSION_CODE com.example.sms_app.utils  CacheManager com.example.sms_app.utils  CancellableContinuation com.example.sms_app.utils  CancellationException com.example.sms_app.utils  Cell com.example.sms_app.utils  CellType com.example.sms_app.utils  Charsets com.example.sms_app.utils  Cipher com.example.sms_app.utils  Class com.example.sms_app.utils  ClassNotFoundException com.example.sms_app.utils  ConnectionResult com.example.sms_app.utils  Context com.example.sms_app.utils  
ContextCompat com.example.sms_app.utils  CoroutineScope com.example.sms_app.utils  CryptoUtils com.example.sms_app.utils  Customer com.example.sms_app.utils  DateUtil com.example.sms_app.utils  DexClassLoader com.example.sms_app.utils  Dispatchers com.example.sms_app.utils  ENCODED_SMS_MANAGER com.example.sms_app.utils  ERROR_RETRY_FAILED com.example.sms_app.utils  EXTRA_CUSTOMER_ID com.example.sms_app.utils  EXTRA_INTERVAL_SECONDS com.example.sms_app.utils  EXTRA_MAX_RETRY com.example.sms_app.utils  
EXTRA_MESSAGE com.example.sms_app.utils  EXTRA_PROGRESS com.example.sms_app.utils  EXTRA_RETRY_DELAY com.example.sms_app.utils  EXTRA_SIM_ID com.example.sms_app.utils  EXTRA_SMS_COUNT com.example.sms_app.utils  EXTRA_TEMPLATE_ID com.example.sms_app.utils  EXTRA_TOTAL com.example.sms_app.utils  
ExcelImporter com.example.sms_app.utils  	Exception com.example.sms_app.utils  File com.example.sms_app.utils  FileOutputStream com.example.sms_app.utils  FileProvider com.example.sms_app.utils  Float com.example.sms_app.utils  GCMParameterSpec com.example.sms_app.utils  GoogleApiAvailability com.example.sms_app.utils  GsonConverterFactory com.example.sms_app.utils  Handler com.example.sms_app.utils  HttpLoggingInterceptor com.example.sms_app.utils  IBinder com.example.sms_app.utils  IllegalArgumentException com.example.sms_app.utils  IllegalStateException com.example.sms_app.utils  Inject com.example.sms_app.utils  InputStream com.example.sms_app.utils  Int com.example.sms_app.utils  Intent com.example.sms_app.utils  IntentFilter com.example.sms_app.utils  IntentUtils com.example.sms_app.utils  Job com.example.sms_app.utils  	JvmStatic com.example.sms_app.utils  KEY com.example.sms_app.utils  Keep com.example.sms_app.utils  KeyGenParameterSpec com.example.sms_app.utils  KeyGenerator com.example.sms_app.utils  
KeyProperties com.example.sms_app.utils  KeyStore com.example.sms_app.utils  List com.example.sms_app.utils  Log com.example.sms_app.utils  Long com.example.sms_app.utils  Looper com.example.sms_app.utils  Manifest com.example.sms_app.utils  
MessageDigest com.example.sms_app.utils  Method com.example.sms_app.utils  MutableList com.example.sms_app.utils  
MutableSet com.example.sms_app.utils  NOTIFICATION_CHANNEL_ID com.example.sms_app.utils  NOTIFICATION_ID com.example.sms_app.utils  NoSuchMethodException com.example.sms_app.utils  Notification com.example.sms_app.utils  NotificationChannel com.example.sms_app.utils  NotificationCompat com.example.sms_app.utils  NotificationManager com.example.sms_app.utils  OkHttpClient com.example.sms_app.utils  PackageManager com.example.sms_app.utils  Pair com.example.sms_app.utils  
PendingIntent com.example.sms_app.utils  Regex com.example.sms_app.utils  RequiresPermission com.example.sms_app.utils  Retrofit com.example.sms_app.utils  Row com.example.sms_app.utils  Runtime com.example.sms_app.utils  SMS_DELIVERED_ACTION com.example.sms_app.utils  SMS_SENT_ACTION com.example.sms_app.utils  START_NOT_STICKY com.example.sms_app.utils  START_REDELIVER_INTENT com.example.sms_app.utils  	SafetyNet com.example.sms_app.utils  	SecretKey com.example.sms_app.utils  SecurityException com.example.sms_app.utils  SecurityReport com.example.sms_app.utils  
SecurityUtils com.example.sms_app.utils  Service com.example.sms_app.utils  
SessionBackup com.example.sms_app.utils  Settings com.example.sms_app.utils  	SimConfig com.example.sms_app.utils  SimInfo com.example.sms_app.utils  
SimManager com.example.sms_app.utils  	Singleton com.example.sms_app.utils  
SmsAttempt com.example.sms_app.utils  
SmsManager com.example.sms_app.utils  
SmsRepository com.example.sms_app.utils  
SmsService com.example.sms_app.utils  SmsTemplate com.example.sms_app.utils  String com.example.sms_app.utils  
StringBuilder com.example.sms_app.utils  SubscriptionManager com.example.sms_app.utils  Suppress com.example.sms_app.utils  SuppressLint com.example.sms_app.utils  System com.example.sms_app.utils  TAG com.example.sms_app.utils  TELEPHONY_SERVICE com.example.sms_app.utils  TelephonyManager com.example.sms_app.utils  Timber com.example.sms_app.utils  Toast com.example.sms_app.utils  UUID com.example.sms_app.utils  Unit com.example.sms_app.utils  UpdateApiService com.example.sms_app.utils  
UpdateInfo com.example.sms_app.utils  Uri com.example.sms_app.utils  WorkbookFactory com.example.sms_app.utils  activeAttempts com.example.sms_app.utils  all com.example.sms_app.utils  also com.example.sms_app.utils  android com.example.sms_app.utils  any com.example.sms_app.utils  
appendLine com.example.sms_app.utils  apply com.example.sms_app.utils  arrayOf com.example.sms_app.utils  async com.example.sms_app.utils  attemptQueue com.example.sms_app.utils  byteArrayOf com.example.sms_app.utils  code com.example.sms_app.utils  coerceIn com.example.sms_app.utils  com com.example.sms_app.utils  
component1 com.example.sms_app.utils  
component2 com.example.sms_app.utils  contains com.example.sms_app.utils  context com.example.sms_app.utils  copyOfRange com.example.sms_app.utils  copyTo com.example.sms_app.utils  coroutineScope com.example.sms_app.utils  d com.example.sms_app.utils  decodeClassName com.example.sms_app.utils  delay com.example.sms_app.utils  distinct com.example.sms_app.utils  e com.example.sms_app.utils  	emptyList com.example.sms_app.utils  endsWith com.example.sms_app.utils  equals com.example.sms_app.utils  executeNextAttempt com.example.sms_app.utils  filter com.example.sms_app.utils  find com.example.sms_app.utils  forEach com.example.sms_app.utils  forEachIndexed com.example.sms_app.utils  format com.example.sms_app.utils  formatPhoneNumber com.example.sms_app.utils  
getAppVersion com.example.sms_app.utils  getDefaultMethod com.example.sms_app.utils  getOrDefault com.example.sms_app.utils  	getOrNull com.example.sms_app.utils  getProp com.example.sms_app.utils  getRandomDelay com.example.sms_app.utils  getSystemService com.example.sms_app.utils  getValue com.example.sms_app.utils  handleSendFailure com.example.sms_app.utils  hasEmulatorBuildProps com.example.sms_app.utils  hasEmulatorTelephony com.example.sms_app.utils  hasRequiredPermissions com.example.sms_app.utils  hasValidVietnamesePrefix com.example.sms_app.utils  ifEmpty com.example.sms_app.utils  indices com.example.sms_app.utils  
initialize com.example.sms_app.utils  invoke com.example.sms_app.utils  isBlank com.example.sms_app.utils  isDigit com.example.sms_app.utils  isEmpty com.example.sms_app.utils  
isEmulator com.example.sms_app.utils  isLetter com.example.sms_app.utils  
isNotBlank com.example.sms_app.utils  
isNotEmpty com.example.sms_app.utils  
isNullOrEmpty com.example.sms_app.utils  	isRunning com.example.sms_app.utils  isUpperCase com.example.sms_app.utils  isValidPhoneNumber com.example.sms_app.utils  isValidVietnameseNumber com.example.sms_app.utils  isWhitespace com.example.sms_app.utils  iterator com.example.sms_app.utils  java com.example.sms_app.utils  joinToString com.example.sms_app.utils  kotlinx com.example.sms_app.utils  launch com.example.sms_app.utils  lazy com.example.sms_app.utils  let com.example.sms_app.utils  listOf com.example.sms_app.utils  loadDynamicCode com.example.sms_app.utils  longArrayOf com.example.sms_app.utils  map com.example.sms_app.utils  mapOf com.example.sms_app.utils  maxOf com.example.sms_app.utils  minOf com.example.sms_app.utils  minusAssign com.example.sms_app.utils  
mutableListOf com.example.sms_app.utils  mutableMapOf com.example.sms_app.utils  	onFailure com.example.sms_app.utils  outputStream com.example.sms_app.utils  padStart com.example.sms_app.utils  pendingSmsDeliveries com.example.sms_app.utils  pendingSmsResults com.example.sms_app.utils  plus com.example.sms_app.utils  
plusAssign com.example.sms_app.utils  provideDelegate com.example.sms_app.utils  random com.example.sms_app.utils  replace com.example.sms_app.utils  resume com.example.sms_app.utils  reversed com.example.sms_app.utils  runCatching com.example.sms_app.utils  sendCompletionBroadcast com.example.sms_app.utils  sendMultipartSmsUsingReflection com.example.sms_app.utils  sendMultipartTextMessageMethod com.example.sms_app.utils  sendProgressBroadcast com.example.sms_app.utils  sendSmsToCustomers com.example.sms_app.utils  sendSmsToVietnameseNumber com.example.sms_app.utils  sendSmsUsingReflection com.example.sms_app.utils  sendSmsWithDeliveryReport com.example.sms_app.utils  sendTextMessageMethod com.example.sms_app.utils  set com.example.sms_app.utils  smsManagerClass com.example.sms_app.utils  split com.example.sms_app.utils  stackTraceToString com.example.sms_app.utils  startSendingSms com.example.sms_app.utils  
startsWith com.example.sms_app.utils  stopSelf com.example.sms_app.utils  	substring com.example.sms_app.utils  suspendCancellableCoroutine com.example.sms_app.utils  tag com.example.sms_app.utils  take com.example.sms_app.utils  to com.example.sms_app.utils  toByteArray com.example.sms_app.utils  toCharArray com.example.sms_app.utils  toIntOrNull com.example.sms_app.utils  toString com.example.sms_app.utils  toTypedArray com.example.sms_app.utils  	totalSent com.example.sms_app.utils  totalToSend com.example.sms_app.utils  trim com.example.sms_app.utils  until com.example.sms_app.utils  use com.example.sms_app.utils  validateAndFormatPhoneNumber com.example.sms_app.utils  w com.example.sms_app.utils  withContext com.example.sms_app.utils  	withIndex com.example.sms_app.utils  BASE_URL *com.example.sms_app.utils.AppUpdateManager  Boolean *com.example.sms_app.utils.AppUpdateManager  Build *com.example.sms_app.utils.AppUpdateManager  	ByteArray *com.example.sms_app.utils.AppUpdateManager  CURRENT_VERSION *com.example.sms_app.utils.AppUpdateManager  CURRENT_VERSION_CODE *com.example.sms_app.utils.AppUpdateManager  Context *com.example.sms_app.utils.AppUpdateManager  Dispatchers *com.example.sms_app.utils.AppUpdateManager  	Exception *com.example.sms_app.utils.AppUpdateManager  File *com.example.sms_app.utils.AppUpdateManager  FileOutputStream *com.example.sms_app.utils.AppUpdateManager  FileProvider *com.example.sms_app.utils.AppUpdateManager  Float *com.example.sms_app.utils.AppUpdateManager  GsonConverterFactory *com.example.sms_app.utils.AppUpdateManager  HttpLoggingInterceptor *com.example.sms_app.utils.AppUpdateManager  Inject *com.example.sms_app.utils.AppUpdateManager  InputStream *com.example.sms_app.utils.AppUpdateManager  Int *com.example.sms_app.utils.AppUpdateManager  Intent *com.example.sms_app.utils.AppUpdateManager  Keep *com.example.sms_app.utils.AppUpdateManager  Long *com.example.sms_app.utils.AppUpdateManager  OkHttpClient *com.example.sms_app.utils.AppUpdateManager  PackageManager *com.example.sms_app.utils.AppUpdateManager  Retrofit *com.example.sms_app.utils.AppUpdateManager  Settings *com.example.sms_app.utils.AppUpdateManager  String *com.example.sms_app.utils.AppUpdateManager  Suppress *com.example.sms_app.utils.AppUpdateManager  Timber *com.example.sms_app.utils.AppUpdateManager  Toast *com.example.sms_app.utils.AppUpdateManager  Unit *com.example.sms_app.utils.AppUpdateManager  UpdateApiService *com.example.sms_app.utils.AppUpdateManager  
UpdateInfo *com.example.sms_app.utils.AppUpdateManager  Uri *com.example.sms_app.utils.AppUpdateManager  also *com.example.sms_app.utils.AppUpdateManager  
apiService *com.example.sms_app.utils.AppUpdateManager  apply *com.example.sms_app.utils.AppUpdateManager  
canInstallApk *com.example.sms_app.utils.AppUpdateManager  checkForUpdates *com.example.sms_app.utils.AppUpdateManager  contains *com.example.sms_app.utils.AppUpdateManager  context *com.example.sms_app.utils.AppUpdateManager  d *com.example.sms_app.utils.AppUpdateManager  downloadAndInstallUpdate *com.example.sms_app.utils.AppUpdateManager  e *com.example.sms_app.utils.AppUpdateManager  getCurrentVersionCode *com.example.sms_app.utils.AppUpdateManager  getCurrentVersionName *com.example.sms_app.utils.AppUpdateManager  	getOrNull *com.example.sms_app.utils.AppUpdateManager  getValue *com.example.sms_app.utils.AppUpdateManager  ifEmpty *com.example.sms_app.utils.AppUpdateManager  
installApk *com.example.sms_app.utils.AppUpdateManager  isNewerVersion *com.example.sms_app.utils.AppUpdateManager  java *com.example.sms_app.utils.AppUpdateManager  lazy *com.example.sms_app.utils.AppUpdateManager  map *com.example.sms_app.utils.AppUpdateManager  maxOf *com.example.sms_app.utils.AppUpdateManager  openInstallPermissionSettings *com.example.sms_app.utils.AppUpdateManager  
plusAssign *com.example.sms_app.utils.AppUpdateManager  provideDelegate *com.example.sms_app.utils.AppUpdateManager  requestInstallPermission *com.example.sms_app.utils.AppUpdateManager  split *com.example.sms_app.utils.AppUpdateManager  testApiConnection *com.example.sms_app.utils.AppUpdateManager  toIntOrNull *com.example.sms_app.utils.AppUpdateManager  until *com.example.sms_app.utils.AppUpdateManager  w *com.example.sms_app.utils.AppUpdateManager  withContext *com.example.sms_app.utils.AppUpdateManager  BASE_URL 4com.example.sms_app.utils.AppUpdateManager.Companion  Build 4com.example.sms_app.utils.AppUpdateManager.Companion  	ByteArray 4com.example.sms_app.utils.AppUpdateManager.Companion  CURRENT_VERSION 4com.example.sms_app.utils.AppUpdateManager.Companion  CURRENT_VERSION_CODE 4com.example.sms_app.utils.AppUpdateManager.Companion  Dispatchers 4com.example.sms_app.utils.AppUpdateManager.Companion  File 4com.example.sms_app.utils.AppUpdateManager.Companion  FileOutputStream 4com.example.sms_app.utils.AppUpdateManager.Companion  FileProvider 4com.example.sms_app.utils.AppUpdateManager.Companion  GsonConverterFactory 4com.example.sms_app.utils.AppUpdateManager.Companion  HttpLoggingInterceptor 4com.example.sms_app.utils.AppUpdateManager.Companion  Intent 4com.example.sms_app.utils.AppUpdateManager.Companion  OkHttpClient 4com.example.sms_app.utils.AppUpdateManager.Companion  Retrofit 4com.example.sms_app.utils.AppUpdateManager.Companion  Settings 4com.example.sms_app.utils.AppUpdateManager.Companion  Timber 4com.example.sms_app.utils.AppUpdateManager.Companion  Toast 4com.example.sms_app.utils.AppUpdateManager.Companion  UpdateApiService 4com.example.sms_app.utils.AppUpdateManager.Companion  
UpdateInfo 4com.example.sms_app.utils.AppUpdateManager.Companion  Uri 4com.example.sms_app.utils.AppUpdateManager.Companion  also 4com.example.sms_app.utils.AppUpdateManager.Companion  apply 4com.example.sms_app.utils.AppUpdateManager.Companion  contains 4com.example.sms_app.utils.AppUpdateManager.Companion  context 4com.example.sms_app.utils.AppUpdateManager.Companion  d 4com.example.sms_app.utils.AppUpdateManager.Companion  e 4com.example.sms_app.utils.AppUpdateManager.Companion  	getOrNull 4com.example.sms_app.utils.AppUpdateManager.Companion  getValue 4com.example.sms_app.utils.AppUpdateManager.Companion  ifEmpty 4com.example.sms_app.utils.AppUpdateManager.Companion  java 4com.example.sms_app.utils.AppUpdateManager.Companion  lazy 4com.example.sms_app.utils.AppUpdateManager.Companion  map 4com.example.sms_app.utils.AppUpdateManager.Companion  maxOf 4com.example.sms_app.utils.AppUpdateManager.Companion  
plusAssign 4com.example.sms_app.utils.AppUpdateManager.Companion  provideDelegate 4com.example.sms_app.utils.AppUpdateManager.Companion  split 4com.example.sms_app.utils.AppUpdateManager.Companion  toIntOrNull 4com.example.sms_app.utils.AppUpdateManager.Companion  until 4com.example.sms_app.utils.AppUpdateManager.Companion  w 4com.example.sms_app.utils.AppUpdateManager.Companion  withContext 4com.example.sms_app.utils.AppUpdateManager.Companion  NameNotFoundException 9com.example.sms_app.utils.AppUpdateManager.PackageManager  downloadUrl 5com.example.sms_app.utils.AppUpdateManager.UpdateInfo  forceUpdate 5com.example.sms_app.utils.AppUpdateManager.UpdateInfo  releaseNotes 5com.example.sms_app.utils.AppUpdateManager.UpdateInfo  versionName 5com.example.sms_app.utils.AppUpdateManager.UpdateInfo  CacheManager )com.example.sms_app.utils.AutoSmsDisabler  Log )com.example.sms_app.utils.AutoSmsDisabler  SecurityReport )com.example.sms_app.utils.AutoSmsDisabler  
SmsRepository )com.example.sms_app.utils.AutoSmsDisabler  
StringBuilder )com.example.sms_app.utils.AutoSmsDisabler  TAG )com.example.sms_app.utils.AutoSmsDisabler  
appendLine )com.example.sms_app.utils.AutoSmsDisabler  checkSecurityStatus )com.example.sms_app.utils.AutoSmsDisabler  com )com.example.sms_app.utils.AutoSmsDisabler  contains )com.example.sms_app.utils.AutoSmsDisabler  disableAllAutoSmsFeatures )com.example.sms_app.utils.AutoSmsDisabler  	emptyList )com.example.sms_app.utils.AutoSmsDisabler  initializeSafeMode )com.example.sms_app.utils.AutoSmsDisabler  
isNotEmpty )com.example.sms_app.utils.AutoSmsDisabler  listOf )com.example.sms_app.utils.AutoSmsDisabler  
mutableListOf )com.example.sms_app.utils.AutoSmsDisabler  Log &com.example.sms_app.utils.CacheManager  
SessionBackup &com.example.sms_app.utils.CacheManager  
SmsRepository &com.example.sms_app.utils.CacheManager  TAG &com.example.sms_app.utils.CacheManager  checkForPotentialAutoSmsCache &com.example.sms_app.utils.CacheManager  clearAllSmsCache &com.example.sms_app.utils.CacheManager  clearCacheAfterImport &com.example.sms_app.utils.CacheManager  context &com.example.sms_app.utils.CacheManager  joinToString &com.example.sms_app.utils.CacheManager  
mutableListOf &com.example.sms_app.utils.CacheManager  ANDROID_KEYSTORE %com.example.sms_app.utils.CryptoUtils  Base64 %com.example.sms_app.utils.CryptoUtils  Charsets %com.example.sms_app.utils.CryptoUtils  Cipher %com.example.sms_app.utils.CryptoUtils  GCMParameterSpec %com.example.sms_app.utils.CryptoUtils  IV_SEPARATOR %com.example.sms_app.utils.CryptoUtils  KeyGenParameterSpec %com.example.sms_app.utils.CryptoUtils  KeyGenerator %com.example.sms_app.utils.CryptoUtils  
KeyProperties %com.example.sms_app.utils.CryptoUtils  KeyStore %com.example.sms_app.utils.CryptoUtils  String %com.example.sms_app.utils.CryptoUtils  
StringBuilder %com.example.sms_app.utils.CryptoUtils  TRANSFORMATION %com.example.sms_app.utils.CryptoUtils  code %com.example.sms_app.utils.CryptoUtils  copyOfRange %com.example.sms_app.utils.CryptoUtils  indexOf %com.example.sms_app.utils.CryptoUtils  indices %com.example.sms_app.utils.CryptoUtils  invoke %com.example.sms_app.utils.CryptoUtils  plus %com.example.sms_app.utils.CryptoUtils  scramble %com.example.sms_app.utils.CryptoUtils  toByteArray %com.example.sms_app.utils.CryptoUtils  
unscramble %com.example.sms_app.utils.CryptoUtils  CacheManager 'com.example.sms_app.utils.ExcelImporter  CellType 'com.example.sms_app.utils.ExcelImporter  Customer 'com.example.sms_app.utils.ExcelImporter  DateUtil 'com.example.sms_app.utils.ExcelImporter  Log 'com.example.sms_app.utils.ExcelImporter  Regex 'com.example.sms_app.utils.ExcelImporter  
StringBuilder 'com.example.sms_app.utils.ExcelImporter  WorkbookFactory 'com.example.sms_app.utils.ExcelImporter  cleanPhoneNumber 'com.example.sms_app.utils.ExcelImporter  code 'com.example.sms_app.utils.ExcelImporter  
component1 'com.example.sms_app.utils.ExcelImporter  
component2 'com.example.sms_app.utils.ExcelImporter  contains 'com.example.sms_app.utils.ExcelImporter  context 'com.example.sms_app.utils.ExcelImporter  debugCharacterAnalysis 'com.example.sms_app.utils.ExcelImporter  deepCleanExcelData 'com.example.sms_app.utils.ExcelImporter  determineCarrier 'com.example.sms_app.utils.ExcelImporter  	emptyList 'com.example.sms_app.utils.ExcelImporter  filter 'com.example.sms_app.utils.ExcelImporter  forEachIndexed 'com.example.sms_app.utils.ExcelImporter  getCellValueAsInt 'com.example.sms_app.utils.ExcelImporter  getCellValueAsString 'com.example.sms_app.utils.ExcelImporter  importCustomers 'com.example.sms_app.utils.ExcelImporter  isBlank 'com.example.sms_app.utils.ExcelImporter  isDigit 'com.example.sms_app.utils.ExcelImporter  
isEmptyRow 'com.example.sms_app.utils.ExcelImporter  isLetter 'com.example.sms_app.utils.ExcelImporter  
isNotBlank 'com.example.sms_app.utils.ExcelImporter  isWhitespace 'com.example.sms_app.utils.ExcelImporter  iterator 'com.example.sms_app.utils.ExcelImporter  java 'com.example.sms_app.utils.ExcelImporter  joinToString 'com.example.sms_app.utils.ExcelImporter  listOf 'com.example.sms_app.utils.ExcelImporter  map 'com.example.sms_app.utils.ExcelImporter  mapOf 'com.example.sms_app.utils.ExcelImporter  
mutableListOf 'com.example.sms_app.utils.ExcelImporter  padStart 'com.example.sms_app.utils.ExcelImporter  replace 'com.example.sms_app.utils.ExcelImporter  
startsWith 'com.example.sms_app.utils.ExcelImporter  	substring 'com.example.sms_app.utils.ExcelImporter  to 'com.example.sms_app.utils.ExcelImporter  toCharArray 'com.example.sms_app.utils.ExcelImporter  toIntOrNull 'com.example.sms_app.utils.ExcelImporter  toString 'com.example.sms_app.utils.ExcelImporter  trim 'com.example.sms_app.utils.ExcelImporter  until 'com.example.sms_app.utils.ExcelImporter  Intent %com.example.sms_app.utils.IntentUtils  Timber %com.example.sms_app.utils.IntentUtils  Toast %com.example.sms_app.utils.IntentUtils  Uri %com.example.sms_app.utils.IntentUtils  apply %com.example.sms_app.utils.IntentUtils  d %com.example.sms_app.utils.IntentUtils  e %com.example.sms_app.utils.IntentUtils  openFacebook %com.example.sms_app.utils.IntentUtils  openZalo %com.example.sms_app.utils.IntentUtils  NameNotFoundException (com.example.sms_app.utils.PackageManager  info (com.example.sms_app.utils.SecurityReport  isSecure (com.example.sms_app.utils.SecurityReport  issues (com.example.sms_app.utils.SecurityReport  warnings (com.example.sms_app.utils.SecurityReport  Base64 'com.example.sms_app.utils.SecurityUtils  Build 'com.example.sms_app.utils.SecurityUtils  BuildConfig 'com.example.sms_app.utils.SecurityUtils  ConnectionResult 'com.example.sms_app.utils.SecurityUtils  DANGEROUS_APPS 'com.example.sms_app.utils.SecurityUtils  DexClassLoader 'com.example.sms_app.utils.SecurityUtils  EMULATOR_PROPERTIES 'com.example.sms_app.utils.SecurityUtils  File 'com.example.sms_app.utils.SecurityUtils  GoogleApiAvailability 'com.example.sms_app.utils.SecurityUtils  Log 'com.example.sms_app.utils.SecurityUtils  
MessageDigest 'com.example.sms_app.utils.SecurityUtils  PackageManager 'com.example.sms_app.utils.SecurityUtils  ROOTED_FILES 'com.example.sms_app.utils.SecurityUtils  	SafetyNet 'com.example.sms_app.utils.SecurityUtils  Settings 'com.example.sms_app.utils.SecurityUtils  
StringBuilder 'com.example.sms_app.utils.SecurityUtils  System 'com.example.sms_app.utils.SecurityUtils  UUID 'com.example.sms_app.utils.SecurityUtils  arrayOf 'com.example.sms_app.utils.SecurityUtils  code 'com.example.sms_app.utils.SecurityUtils  contains 'com.example.sms_app.utils.SecurityUtils  copyTo 'com.example.sms_app.utils.SecurityUtils  decode 'com.example.sms_app.utils.SecurityUtils  indices 'com.example.sms_app.utils.SecurityUtils  isLetter 'com.example.sms_app.utils.SecurityUtils  
isNotEmpty 'com.example.sms_app.utils.SecurityUtils  isRooted 'com.example.sms_app.utils.SecurityUtils  isUpperCase 'com.example.sms_app.utils.SecurityUtils  joinToString 'com.example.sms_app.utils.SecurityUtils  map 'com.example.sms_app.utils.SecurityUtils  outputStream 'com.example.sms_app.utils.SecurityUtils  reversed 'com.example.sms_app.utils.SecurityUtils  toByteArray 'com.example.sms_app.utils.SecurityUtils  toTypedArray 'com.example.sms_app.utils.SecurityUtils  use 'com.example.sms_app.utils.SecurityUtils  verifyAppIntegrity 'com.example.sms_app.utils.SecurityUtils  verifyAppSignature 'com.example.sms_app.utils.SecurityUtils  allSims #com.example.sms_app.utils.SimConfig  	isDualSim #com.example.sms_app.utils.SimConfig  
primarySim #com.example.sms_app.utils.SimConfig  carrierName !com.example.sms_app.utils.SimInfo  displayName !com.example.sms_app.utils.SimInfo  phoneNumber !com.example.sms_app.utils.SimInfo  simSlotIndex !com.example.sms_app.utils.SimInfo  subscriptionId !com.example.sms_app.utils.SimInfo  Context $com.example.sms_app.utils.SimManager  
ContextCompat $com.example.sms_app.utils.SimManager  Manifest $com.example.sms_app.utils.SimManager  PackageManager $com.example.sms_app.utils.SimManager  SimInfo $com.example.sms_app.utils.SimManager  android $com.example.sms_app.utils.SimManager  find $com.example.sms_app.utils.SimManager  getAvailableSims $com.example.sms_app.utils.SimManager  hasRequiredPermissions $com.example.sms_app.utils.SimManager  
mutableListOf $com.example.sms_app.utils.SimManager  app !com.example.sms_app.utils.android  Notification %com.example.sms_app.utils.android.app  lang com.example.sms_app.utils.java  reflect #com.example.sms_app.utils.java.lang  InvocationTargetException +com.example.sms_app.utils.java.lang.reflect  
coroutines !com.example.sms_app.utils.kotlinx  CancellableContinuation ,com.example.sms_app.utils.kotlinx.coroutines  ConnectionResult com.google.android.gms.common  GoogleApiAvailability com.google.android.gms.common  SUCCESS .com.google.android.gms.common.ConnectionResult  getInstance 3com.google.android.gms.common.GoogleApiAvailability  isGooglePlayServicesAvailable 3com.google.android.gms.common.GoogleApiAvailability  	SafetyNet  com.google.android.gms.safetynet  SafetyNetClient  com.google.android.gms.safetynet  	getClient *com.google.android.gms.safetynet.SafetyNet  attest 0com.google.android.gms.safetynet.SafetyNetClient  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  DexClassLoader 
dalvik.system  	loadClass dalvik.system.DexClassLoader  File java.io  FileOutputStream java.io  InputStream java.io  absolutePath java.io.File  
appendText java.io.File  canRead java.io.File  delete java.io.File  exists java.io.File  isDirectory java.io.File  isFile java.io.File  lastModified java.io.File  length java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  outputStream java.io.File  
parentFile java.io.File  toPath java.io.File  close java.io.FileOutputStream  use java.io.FileOutputStream  write java.io.FileOutputStream  close java.io.InputStream  copyTo java.io.InputStream  read java.io.InputStream  use java.io.InputStream  
Appendable 	java.lang  Class 	java.lang  ClassNotFoundException 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  NoSuchMethodException 	java.lang  Runnable 	java.lang  SecurityException 	java.lang  
StringBuilder 	java.lang  forName java.lang.Class  	getMethod java.lang.Class  	loadClass java.lang.ClassLoader  cause java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  stackTraceToString java.lang.Exception  message java.lang.IllegalStateException  inputStream java.lang.Process  <SAM-CONSTRUCTOR> java.lang.Runnable  exec java.lang.Runtime  
getRuntime java.lang.Runtime  message java.lang.SecurityException  append java.lang.StringBuilder  
appendLine java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  getProperty java.lang.System  InvocationTargetException java.lang.reflect  Method java.lang.reflect  targetException +java.lang.reflect.InvocationTargetException  invoke java.lang.reflect.Method  
BigDecimal 	java.math  
BigInteger 	java.math  Charset java.nio.charset  Files 
java.nio.file  write java.nio.file.Files  resolve java.nio.file.Path  KeyStore 
java.security  
MessageDigest 
java.security  
containsAlias java.security.KeyStore  getInstance java.security.KeyStore  getKey java.security.KeyStore  load java.security.KeyStore  digest java.security.MessageDigest  getInstance java.security.MessageDigest  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  Form java.text.Normalizer  	normalize java.text.Normalizer  NFC java.text.Normalizer.Form  NFD java.text.Normalizer.Form  NFKC java.text.Normalizer.Form  NFKD java.text.Normalizer.Form  format java.text.SimpleDateFormat  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  	ArrayList 	java.util  Base64 	java.util  Boolean 	java.util  Box 	java.util  Build 	java.util  BuildConfig 	java.util  Button 	java.util  ButtonDefaults 	java.util  Card 	java.util  CardDefaults 	java.util  CircularProgressIndicator 	java.util  Color 	java.util  Column 	java.util  
Comparator 	java.util  
Composable 	java.util  ConnectionResult 	java.util  Context 	java.util  Customer 	java.util  Date 	java.util  DexClassLoader 	java.util  Divider 	java.util  Environment 	java.util  	Exception 	java.util  File 	java.util  FileItem 	java.util  FileItemRow 	java.util  
FontWeight 	java.util  GoogleApiAvailability 	java.util  Gson 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  Int 	java.util  	JvmStatic 	java.util  Keep 	java.util  LaunchedEffect 	java.util  
LazyColumn 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  Long 	java.util  
MessageDigest 	java.util  Modifier 	java.util  MutableList 	java.util  PackageManager 	java.util  Row 	java.util  	SafetyNet 	java.util  Scanner 	java.util  SecurityException 	java.util  Settings 	java.util  SharedPreferences 	java.util  SimpleDateFormat 	java.util  
SmsSession 	java.util  Spacer 	java.util  String 	java.util  
StringBuilder 	java.util  Suppress 	java.util  System 	java.util  Text 	java.util  	TextAlign 	java.util  
TextButton 	java.util  	TypeToken 	java.util  UUID 	java.util  Unit 	java.util  android 	java.util  arrayOf 	java.util  
background 	java.util  buttonColors 	java.util  
cardColors 	java.util  code 	java.util  	compareBy 	java.util  contains 	java.util  copyTo 	java.util  	emptyList 	java.util  endsWith 	java.util  
fillMaxHeight 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  filter 	java.util  find 	java.util  first 	java.util  getValue 	java.util  height 	java.util  indices 	java.util  isEmpty 	java.util  isLetter 	java.util  
isNotEmpty 	java.util  isUpperCase 	java.util  java 	java.util  joinToString 	java.util  lazy 	java.util  let 	java.util  	lowercase 	java.util  map 	java.util  
mapNotNull 	java.util  
mutableListOf 	java.util  mutableStateOf 	java.util  outputStream 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  	removeAll 	java.util  replace 	java.util  reversed 	java.util  setValue 	java.util  size 	java.util  
sortedWith 	java.util  spacedBy 	java.util  
startsWith 	java.util  take 	java.util  takeIf 	java.util  textButtonColors 	java.util  thenBy 	java.util  toByteArray 	java.util  
toMutableList 	java.util  toTypedArray 	java.util  use 	java.util  weight 	java.util  width 	java.util  add java.util.ArrayList  size java.util.ArrayList  thenBy java.util.Comparator  time java.util.Date  
getDefault java.util.Locale  hasNextLine java.util.Scanner  nextLine java.util.Scanner  
randomUUID java.util.UUID  toString java.util.UUID  CancellationException java.util.concurrent  Cipher javax.crypto  KeyGenerator javax.crypto  	SecretKey javax.crypto  DECRYPT_MODE javax.crypto.Cipher  ENCRYPT_MODE javax.crypto.Cipher  doFinal javax.crypto.Cipher  getInstance javax.crypto.Cipher  init javax.crypto.Cipher  iv javax.crypto.Cipher  generateKey javax.crypto.KeyGenerator  getInstance javax.crypto.KeyGenerator  init javax.crypto.KeyGenerator  GCMParameterSpec javax.crypto.spec  Inject javax.inject  	Singleton javax.inject  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  
Comparable kotlin  DoubleArray kotlin  Enum kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Lazy kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  TODO kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  arrayOf kotlin  byteArrayOf kotlin  code kotlin  getCode kotlin  getOrDefault kotlin  getValue kotlin  lazy kotlin  let kotlin  longArrayOf kotlin  map kotlin  minus kotlin  	onFailure kotlin  plus kotlin  runCatching kotlin  stackTraceToString kotlin  takeIf kotlin  to kotlin  toString kotlin  use kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  all kotlin.Array  get kotlin.Array  iterator kotlin.Array  map kotlin.Array  let kotlin.Boolean  not kotlin.Boolean  takeIf kotlin.Boolean  to kotlin.Boolean  toString kotlin.Boolean  toInt kotlin.Byte  get kotlin.ByteArray  indices kotlin.ByteArray  plus kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  code kotlin.Char  isDigit kotlin.Char  isLetter kotlin.Char  isUpperCase kotlin.Char  isWhitespace kotlin.Char  rangeTo kotlin.Char  toString kotlin.Char  filter kotlin.CharArray  toString kotlin.CharSequence  dp 
kotlin.Double  sp 
kotlin.Double  toInt 
kotlin.Double  toLong 
kotlin.Double  toString 
kotlin.Double  AppSettings kotlin.Enum  Boolean kotlin.Enum  BuildConfig kotlin.Enum  Call kotlin.Enum  
CloudDownload kotlin.Enum  	Companion kotlin.Enum  Delay kotlin.Enum  DocumentScanner kotlin.Enum  	ExitToApp kotlin.Enum  	FilterAlt kotlin.Enum  Home kotlin.Enum  Icons kotlin.Enum  ImageVector kotlin.Enum  Int kotlin.Enum  KeyboardType kotlin.Enum  Limit kotlin.Enum  Message kotlin.Enum  MoreVert kotlin.Enum  Notes kotlin.Enum  Person kotlin.Enum  	PersonAdd kotlin.Enum  Search kotlin.Enum  Send kotlin.Enum  Settings kotlin.Enum  String kotlin.Enum  Update kotlin.Enum  BuildConfig kotlin.Enum.Companion  Call kotlin.Enum.Companion  
CloudDownload kotlin.Enum.Companion  Delay kotlin.Enum.Companion  DocumentScanner kotlin.Enum.Companion  	ExitToApp kotlin.Enum.Companion  	FilterAlt kotlin.Enum.Companion  Home kotlin.Enum.Companion  Icons kotlin.Enum.Companion  KeyboardType kotlin.Enum.Companion  Limit kotlin.Enum.Companion  Message kotlin.Enum.Companion  MoreVert kotlin.Enum.Companion  Notes kotlin.Enum.Companion  Person kotlin.Enum.Companion  	PersonAdd kotlin.Enum.Companion  Search kotlin.Enum.Companion  Send kotlin.Enum.Companion  Settings kotlin.Enum.Companion  Update kotlin.Enum.Companion  div kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  also 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  minusAssign 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toChar 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  until 
kotlin.Int  xor 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  coerceIn kotlin.Long  	compareTo kotlin.Long  div kotlin.Long  formatDuration kotlin.Long  listOf kotlin.Long  maxOf kotlin.Long  minus kotlin.Long  minusAssign kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  rem kotlin.Long  times kotlin.Long  toDouble kotlin.Long  toFloat kotlin.Long  toInt kotlin.Long  toString kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  getOrDefault 
kotlin.Result  	onFailure 
kotlin.Result  	Companion 
kotlin.String  IllegalArgumentException 
kotlin.String  Regex 
kotlin.String  TAG 
kotlin.String  Timber 
kotlin.String  
capitalize 
kotlin.String  code 
kotlin.String  
component1 
kotlin.String  
component2 
kotlin.String  contains 
kotlin.String  endsWith 
kotlin.String  equals 
kotlin.String  forEachIndexed 
kotlin.String  format 
kotlin.String  get 
kotlin.String  getOrDefault 
kotlin.String  hasValidVietnamesePrefix 
kotlin.String  hashCode 
kotlin.String  ifEmpty 
kotlin.String  indices 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  isValidPhoneNumber 
kotlin.String  iterator 
kotlin.String  java 
kotlin.String  joinToString 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  map 
kotlin.String  mapOf 
kotlin.String  	onFailure 
kotlin.String  padStart 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  reversed 
kotlin.String  runCatching 
kotlin.String  split 
kotlin.String  stackTraceToString 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  tag 
kotlin.String  take 
kotlin.String  takeIf 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toCharArray 
kotlin.String  toInt 
kotlin.String  toIntOrNull 
kotlin.String  toString 
kotlin.String  trim 
kotlin.String  	uppercase 
kotlin.String  validateAndFormatPhoneNumber 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  cause kotlin.Throwable  message kotlin.Throwable  printStackTrace kotlin.Throwable  stackTraceToString kotlin.Throwable  ByteIterator kotlin.collections  CharIterator kotlin.collections  
Collection kotlin.collections  IndexedValue kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  
associateWith kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  copyOfRange kotlin.collections  copyOfRangeInline kotlin.collections  count kotlin.collections  distinct kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  first kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  getOrDefault kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  groupBy kotlin.collections  ifEmpty kotlin.collections  indexOfFirst kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  minus kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  random kotlin.collections  	removeAll kotlin.collections  reversed kotlin.collections  set kotlin.collections  setOf kotlin.collections  sortedBy kotlin.collections  
sortedWith kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toCharArray kotlin.collections  
toMutableList kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  	withIndex kotlin.collections  hasNext kotlin.collections.CharIterator  next kotlin.collections.CharIterator  count kotlin.collections.Collection  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  all kotlin.collections.List  any kotlin.collections.List  
associateWith kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  distinct kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  first kotlin.collections.List  firstOrNull kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  groupBy kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  plus kotlin.collections.List  random kotlin.collections.List  size kotlin.collections.List  sortedBy kotlin.collections.List  
sortedWith kotlin.collections.List  take kotlin.collections.List  
toMutableList kotlin.collections.List  toTypedArray kotlin.collections.List  	withIndex kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  iterator kotlin.collections.Map  map kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  MessageTemplate kotlin.collections.MutableList  add kotlin.collections.MutableList  also kotlin.collections.MutableList  apply kotlin.collections.MutableList  filter kotlin.collections.MutableList  get kotlin.collections.MutableList  indexOfFirst kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  
isNullOrEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  	removeAll kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  set kotlin.collections.MutableList  size kotlin.collections.MutableList  take kotlin.collections.MutableList  	withIndex kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  values kotlin.collections.MutableMap  contains kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  minus kotlin.collections.Set  plus kotlin.collections.Set  	compareBy kotlin.comparisons  maxOf kotlin.comparisons  minOf kotlin.comparisons  reversed kotlin.comparisons  thenBy kotlin.comparisons  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  EnumEntries kotlin.enums  any kotlin.enums.EnumEntries  get kotlin.enums.EnumEntries  map kotlin.enums.EnumEntries  size kotlin.enums.EnumEntries  
appendText 	kotlin.io  copyTo 	kotlin.io  endsWith 	kotlin.io  iterator 	kotlin.io  outputStream 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  	JvmStatic 
kotlin.jvm  java 
kotlin.jvm  Random 
kotlin.random  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  firstOrNull 
kotlin.ranges  random 
kotlin.ranges  reversed 
kotlin.ranges  until 
kotlin.ranges  contains kotlin.ranges.CharRange  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  map kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  
associateWith kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  distinct kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  first kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  groupBy kotlin.sequences  ifEmpty kotlin.sequences  indexOfFirst kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  minus kotlin.sequences  plus kotlin.sequences  sortedBy kotlin.sequences  
sortedWith kotlin.sequences  take kotlin.sequences  
toMutableList kotlin.sequences  	withIndex kotlin.sequences  exitProcess 
kotlin.system  Charsets kotlin.text  Regex kotlin.text  String kotlin.text  all kotlin.text  any kotlin.text  
appendLine kotlin.text  
associateWith kotlin.text  contains kotlin.text  count kotlin.text  endsWith kotlin.text  equals kotlin.text  filter kotlin.text  find kotlin.text  first kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  	getOrNull kotlin.text  groupBy kotlin.text  ifEmpty kotlin.text  indexOfFirst kotlin.text  indices kotlin.text  isBlank kotlin.text  isDigit kotlin.text  isEmpty kotlin.text  isLetter kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  isUpperCase kotlin.text  isWhitespace kotlin.text  iterator kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapNotNull kotlin.text  maxOf kotlin.text  minOf kotlin.text  padStart kotlin.text  plus kotlin.text  random kotlin.text  replace kotlin.text  reversed kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  take kotlin.text  toByteArray kotlin.text  toCharArray kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  
toMutableList kotlin.text  toString kotlin.text  trim kotlin.text  	uppercase kotlin.text  	withIndex kotlin.text  UTF_8 kotlin.text.Charsets  ACTION_CUSTOMER_DELETED kotlinx.coroutines  ACTION_PROGRESS_UPDATE kotlinx.coroutines  ACTION_SMS_COMPLETED kotlinx.coroutines  ACTION_SMS_COUNT_UPDATED kotlinx.coroutines  Activity kotlinx.coroutines  AndroidEntryPoint kotlinx.coroutines  	ArrayList kotlinx.coroutines  Boolean kotlinx.coroutines  BroadcastReceiver kotlinx.coroutines  Build kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CancellableContinuation kotlinx.coroutines  CancellationException kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Customer kotlinx.coroutines  Deferred kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  ERROR_RETRY_FAILED kotlinx.coroutines  EXTRA_CUSTOMER_ID kotlinx.coroutines  EXTRA_INTERVAL_SECONDS kotlinx.coroutines  EXTRA_MAX_RETRY kotlinx.coroutines  
EXTRA_MESSAGE kotlinx.coroutines  EXTRA_PROGRESS kotlinx.coroutines  EXTRA_RETRY_DELAY kotlinx.coroutines  EXTRA_SIM_ID kotlinx.coroutines  EXTRA_SMS_COUNT kotlinx.coroutines  EXTRA_TEMPLATE_ID kotlinx.coroutines  EXTRA_TOTAL kotlinx.coroutines  	Exception kotlinx.coroutines  Handler kotlinx.coroutines  IBinder kotlinx.coroutines  IllegalStateException kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  IntentFilter kotlinx.coroutines  Job kotlinx.coroutines  List kotlinx.coroutines  Log kotlinx.coroutines  Long kotlinx.coroutines  Looper kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Manifest kotlinx.coroutines  MutableList kotlinx.coroutines  
MutableSet kotlinx.coroutines  NOTIFICATION_CHANNEL_ID kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  Pair kotlinx.coroutines  
PendingIntent kotlinx.coroutines  Regex kotlinx.coroutines  RequiresPermission kotlinx.coroutines  SMS_DELIVERED_ACTION kotlinx.coroutines  SMS_SENT_ACTION kotlinx.coroutines  START_NOT_STICKY kotlinx.coroutines  START_REDELIVER_INTENT kotlinx.coroutines  SecurityException kotlinx.coroutines  Service kotlinx.coroutines  Settings kotlinx.coroutines  
SmsAttempt kotlinx.coroutines  
SmsManager kotlinx.coroutines  
SmsRepository kotlinx.coroutines  SmsTemplate kotlinx.coroutines  String kotlinx.coroutines  SubscriptionManager kotlinx.coroutines  SuppressLint kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  TELEPHONY_SERVICE kotlinx.coroutines  TelephonyManager kotlinx.coroutines  TimeoutCancellationException kotlinx.coroutines  activeAttempts kotlinx.coroutines  android kotlinx.coroutines  any kotlinx.coroutines  apply kotlinx.coroutines  arrayOf kotlinx.coroutines  async kotlinx.coroutines  attemptQueue kotlinx.coroutines  coerceIn kotlinx.coroutines  com kotlinx.coroutines  contains kotlinx.coroutines  coroutineScope kotlinx.coroutines  delay kotlinx.coroutines  distinct kotlinx.coroutines  endsWith kotlinx.coroutines  executeNextAttempt kotlinx.coroutines  filter kotlinx.coroutines  find kotlinx.coroutines  forEach kotlinx.coroutines  format kotlinx.coroutines  getRandomDelay kotlinx.coroutines  getSystemService kotlinx.coroutines  handleSendFailure kotlinx.coroutines  hasRequiredPermissions kotlinx.coroutines  indices kotlinx.coroutines  
initialize kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  
isNullOrEmpty kotlinx.coroutines  	isRunning kotlinx.coroutines  java kotlinx.coroutines  joinToString kotlinx.coroutines  kotlinx kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  listOf kotlinx.coroutines  longArrayOf kotlinx.coroutines  map kotlinx.coroutines  maxOf kotlinx.coroutines  minOf kotlinx.coroutines  minusAssign kotlinx.coroutines  
mutableListOf kotlinx.coroutines  mutableMapOf kotlinx.coroutines  pendingSmsDeliveries kotlinx.coroutines  pendingSmsResults kotlinx.coroutines  
plusAssign kotlinx.coroutines  random kotlinx.coroutines  replace kotlinx.coroutines  resume kotlinx.coroutines  sendCompletionBroadcast kotlinx.coroutines  sendProgressBroadcast kotlinx.coroutines  sendSmsToCustomers kotlinx.coroutines  sendSmsWithDeliveryReport kotlinx.coroutines  set kotlinx.coroutines  startSendingSms kotlinx.coroutines  
startsWith kotlinx.coroutines  stopSelf kotlinx.coroutines  	substring kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  take kotlinx.coroutines  to kotlinx.coroutines  	totalSent kotlinx.coroutines  totalToSend kotlinx.coroutines  trim kotlinx.coroutines  withContext kotlinx.coroutines  	withIndex kotlinx.coroutines  cancel *kotlinx.coroutines.CancellableContinuation  isActive *kotlinx.coroutines.CancellableContinuation  resume *kotlinx.coroutines.CancellableContinuation  	ByteArray !kotlinx.coroutines.CoroutineScope  
ContextCompat !kotlinx.coroutines.CoroutineScope  Customer !kotlinx.coroutines.CoroutineScope  
CustomerField !kotlinx.coroutines.CoroutineScope  Date !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  Environment !kotlinx.coroutines.CoroutineScope  
ExcelImporter !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  Files !kotlinx.coroutines.CoroutineScope  Intent !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  MessageTemplate !kotlinx.coroutines.CoroutineScope  
SessionBackup !kotlinx.coroutines.CoroutineScope  SimpleDateFormat !kotlinx.coroutines.CoroutineScope  SmsProgress !kotlinx.coroutines.CoroutineScope  
SmsService !kotlinx.coroutines.CoroutineScope  
StringBuilder !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  TemplateManager !kotlinx.coroutines.CoroutineScope  Timber !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  _appSettings !kotlinx.coroutines.CoroutineScope  _completion !kotlinx.coroutines.CoroutineScope  
_customers !kotlinx.coroutines.CoroutineScope  _default !kotlinx.coroutines.CoroutineScope  _downloadProgress !kotlinx.coroutines.CoroutineScope  _isDownloading !kotlinx.coroutines.CoroutineScope  
_isSending !kotlinx.coroutines.CoroutineScope  _messageTemplate !kotlinx.coroutines.CoroutineScope  _millisUntilFinished !kotlinx.coroutines.CoroutineScope  	_progress !kotlinx.coroutines.CoroutineScope  _selectedSim !kotlinx.coroutines.CoroutineScope  _showUpdateDialog !kotlinx.coroutines.CoroutineScope  _updateInfo !kotlinx.coroutines.CoroutineScope  activeAttempts !kotlinx.coroutines.CoroutineScope  also !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  appUpdateManager !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  async !kotlinx.coroutines.CoroutineScope  attemptQueue !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  count !kotlinx.coroutines.CoroutineScope  currentCountDownTimer !kotlinx.coroutines.CoroutineScope  d !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  e !kotlinx.coroutines.CoroutineScope  edit !kotlinx.coroutines.CoroutineScope  executeNextAttempt !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  forEachIndexed !kotlinx.coroutines.CoroutineScope  getApplication !kotlinx.coroutines.CoroutineScope  getDefaultTemplates !kotlinx.coroutines.CoroutineScope  getOrDefault !kotlinx.coroutines.CoroutineScope  getValue !kotlinx.coroutines.CoroutineScope  groupBy !kotlinx.coroutines.CoroutineScope  handleSendFailure !kotlinx.coroutines.CoroutineScope  indexOfFirst !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  isEmpty !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  	isRunning !kotlinx.coroutines.CoroutineScope  isValidPhoneNumber !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  	lowercase !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  maxOf !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  pendingSmsResults !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  resume !kotlinx.coroutines.CoroutineScope  runCatching !kotlinx.coroutines.CoroutineScope  sendCompletionBroadcast !kotlinx.coroutines.CoroutineScope  sendSmsToCustomers !kotlinx.coroutines.CoroutineScope  sendSmsWithDeliveryReport !kotlinx.coroutines.CoroutineScope  
smsRepository !kotlinx.coroutines.CoroutineScope  sortedBy !kotlinx.coroutines.CoroutineScope  startCountdownTimer !kotlinx.coroutines.CoroutineScope  startSendingSms !kotlinx.coroutines.CoroutineScope  stopSelf !kotlinx.coroutines.CoroutineScope  sync !kotlinx.coroutines.CoroutineScope  tag !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  takeIf !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toByteArray !kotlinx.coroutines.CoroutineScope  toIntOrNull !kotlinx.coroutines.CoroutineScope  
toMutableList !kotlinx.coroutines.CoroutineScope  totalCustomers !kotlinx.coroutines.CoroutineScope  	totalSent !kotlinx.coroutines.CoroutineScope  totalToSend !kotlinx.coroutines.CoroutineScope  validateAndFormatPhoneNumber !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  await kotlinx.coroutines.Deferred  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  isActive kotlinx.coroutines.Job  join kotlinx.coroutines.Job  app kotlinx.coroutines.android  Notification kotlinx.coroutines.android.app  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  
coroutines kotlinx.coroutines.kotlinx  CancellableContinuation %kotlinx.coroutines.kotlinx.coroutines  OkHttpClient okhttp3  ResponseBody okhttp3  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  
byteStream okhttp3.ResponseBody  
contentLength okhttp3.ResponseBody  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  HSSFWorkbook org.apache.poi.hssf.usermodel  Boolean org.apache.poi.ss.usermodel  CacheManager org.apache.poi.ss.usermodel  Cell org.apache.poi.ss.usermodel  CellType org.apache.poi.ss.usermodel  Context org.apache.poi.ss.usermodel  Customer org.apache.poi.ss.usermodel  DateUtil org.apache.poi.ss.usermodel  	Exception org.apache.poi.ss.usermodel  InputStream org.apache.poi.ss.usermodel  Int org.apache.poi.ss.usermodel  List org.apache.poi.ss.usermodel  Log org.apache.poi.ss.usermodel  Regex org.apache.poi.ss.usermodel  Row org.apache.poi.ss.usermodel  String org.apache.poi.ss.usermodel  
StringBuilder org.apache.poi.ss.usermodel  Uri org.apache.poi.ss.usermodel  WorkbookFactory org.apache.poi.ss.usermodel  code org.apache.poi.ss.usermodel  
component1 org.apache.poi.ss.usermodel  
component2 org.apache.poi.ss.usermodel  contains org.apache.poi.ss.usermodel  	emptyList org.apache.poi.ss.usermodel  filter org.apache.poi.ss.usermodel  forEach org.apache.poi.ss.usermodel  forEachIndexed org.apache.poi.ss.usermodel  isBlank org.apache.poi.ss.usermodel  isDigit org.apache.poi.ss.usermodel  isLetter org.apache.poi.ss.usermodel  
isNotBlank org.apache.poi.ss.usermodel  isWhitespace org.apache.poi.ss.usermodel  iterator org.apache.poi.ss.usermodel  java org.apache.poi.ss.usermodel  joinToString org.apache.poi.ss.usermodel  listOf org.apache.poi.ss.usermodel  map org.apache.poi.ss.usermodel  mapOf org.apache.poi.ss.usermodel  
mutableListOf org.apache.poi.ss.usermodel  padStart org.apache.poi.ss.usermodel  replace org.apache.poi.ss.usermodel  
startsWith org.apache.poi.ss.usermodel  	substring org.apache.poi.ss.usermodel  to org.apache.poi.ss.usermodel  toCharArray org.apache.poi.ss.usermodel  toIntOrNull org.apache.poi.ss.usermodel  toString org.apache.poi.ss.usermodel  trim org.apache.poi.ss.usermodel  until org.apache.poi.ss.usermodel  booleanCellValue  org.apache.poi.ss.usermodel.Cell  cellType  org.apache.poi.ss.usermodel.Cell  
dateCellValue  org.apache.poi.ss.usermodel.Cell  numericCellValue  org.apache.poi.ss.usermodel.Cell  sheet  org.apache.poi.ss.usermodel.Cell  stringCellValue  org.apache.poi.ss.usermodel.Cell  BOOLEAN $org.apache.poi.ss.usermodel.CellType  FORMULA $org.apache.poi.ss.usermodel.CellType  NUMERIC $org.apache.poi.ss.usermodel.CellType  STRING $org.apache.poi.ss.usermodel.CellType  booleanValue %org.apache.poi.ss.usermodel.CellValue  cellType %org.apache.poi.ss.usermodel.CellValue  numberValue %org.apache.poi.ss.usermodel.CellValue  stringValue %org.apache.poi.ss.usermodel.CellValue  createFormulaEvaluator *org.apache.poi.ss.usermodel.CreationHelper  isCellDateFormatted $org.apache.poi.ss.usermodel.DateUtil  evaluate ,org.apache.poi.ss.usermodel.FormulaEvaluator  getCell org.apache.poi.ss.usermodel.Row  getRow !org.apache.poi.ss.usermodel.Sheet  physicalNumberOfRows !org.apache.poi.ss.usermodel.Sheet  workbook !org.apache.poi.ss.usermodel.Sheet  creationHelper $org.apache.poi.ss.usermodel.Workbook  
getSheetAt $org.apache.poi.ss.usermodel.Workbook  create +org.apache.poi.ss.usermodel.WorkbookFactory  XSSFWorkbook org.apache.poi.xssf.usermodel  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  isSuccessful retrofit2.Response  message retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  GET retrofit2.http  	Streaming retrofit2.http  Timber 
timber.log  	DebugTree timber.log.Timber  Forest timber.log.Timber  Tree timber.log.Timber  d timber.log.Timber  e timber.log.Timber  i timber.log.Timber  plant timber.log.Timber  tag timber.log.Timber  w timber.log.Timber  Boolean timber.log.Timber.DebugTree  	Companion timber.log.Timber.DebugTree  	Exception timber.log.Timber.DebugTree  File timber.log.Timber.DebugTree  Int timber.log.Timber.DebugTree  Log timber.log.Timber.DebugTree  String timber.log.Timber.DebugTree  	Throwable timber.log.Timber.DebugTree  
appendText timber.log.Timber.DebugTree  applicationContext timber.log.Timber.DebugTree  log timber.log.Timber.DebugTree  File %timber.log.Timber.DebugTree.Companion  Log %timber.log.Timber.DebugTree.Companion  
appendText %timber.log.Timber.DebugTree.Companion  applicationContext %timber.log.Timber.DebugTree.Companion  d timber.log.Timber.Forest  e timber.log.Timber.Forest  i timber.log.Timber.Forest  plant timber.log.Timber.Forest  tag timber.log.Timber.Forest  w timber.log.Timber.Forest  d timber.log.Timber.Tree  e timber.log.Timber.Tree  i timber.log.Timber.Tree  log timber.log.Timber.Tree  w timber.log.Timber.Tree  firstOrNull com.example.sms_app.utils  cleanPhoneNumbers 'com.example.sms_app.utils.ExcelImporter  cleanSinglePhoneNumber 'com.example.sms_app.utils.ExcelImporter  firstOrNull 'com.example.sms_app.utils.ExcelImporter  split 'com.example.sms_app.utils.ExcelImporter  firstOrNull org.apache.poi.ss.usermodel  split org.apache.poi.ss.usermodel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                