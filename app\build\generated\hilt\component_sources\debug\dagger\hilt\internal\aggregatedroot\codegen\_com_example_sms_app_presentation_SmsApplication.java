package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.sms_app.presentation.SmsApplication",
    rootPackage = "com.example.sms_app.presentation",
    originatingRoot = "com.example.sms_app.presentation.SmsApplication",
    originatingRootPackage = "com.example.sms_app.presentation",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "SmsApplication",
    originatingRootSimpleNames = "SmsApplication"
)
public class _com_example_sms_app_presentation_SmsApplication {
}
