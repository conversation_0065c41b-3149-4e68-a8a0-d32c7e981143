{"src\\main\\java\\com\\example\\sms_app\\presentation\\component\\WrongPatternDialog.kt": ["WrongPatternDialog:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\utils\\CacheManager.kt": ["clearAllSmsCache:com.example.sms_app.utils.CacheManager", "CacheManager:com.example.sms_app.utils", "disableAllAutoSmsFeatures:com.example.sms_app.utils.CacheManager", "checkForPotentialAutoSmsCache:com.example.sms_app.utils.CacheManager", "clearCacheAfterImport:com.example.sms_app.utils.CacheManager"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\UpdateViewModel.kt": ["getCurrentVersion:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "showUpdateDialog:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "UpdateViewModel:com.example.sms_app.presentation.viewmodel", "checkForUpdates:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "downloadProgress:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "dismissUpdateDialog:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "updateInfo:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "testApiConnection:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "startUpdate:com.example.sms_app.presentation.viewmodel.UpdateViewModel", "isDownloading:com.example.sms_app.presentation.viewmodel.UpdateViewModel"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\PatternViewModel.kt": ["PatternViewModel:com.example.sms_app.presentation.viewmodel", "saveTemplate:com.example.sms_app.presentation.viewmodel.PatternViewModel", "messageTemplate:com.example.sms_app.presentation.viewmodel.PatternViewModel", "default:com.example.sms_app.presentation.viewmodel.PatternViewModel", "selectedSim:com.example.sms_app.presentation.viewmodel.PatternViewModel", "sync:com.example.sms_app.presentation.viewmodel.PatternViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_BindsModule", "_com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\utils\\SmsUtils.kt": ["sendMultipartSmsUsingReflection:com.example.sms_app.utils", "isValidPhoneNumber:com.example.sms_app.utils", "loadDynamicCode:com.example.sms_app.utils", "hasRequiredPermissions:com.example.sms_app.utils", "getRandomDelay:com.example.sms_app.utils", "formatPhoneNumber:com.example.sms_app.utils", "isEmulator:com.example.sms_app.utils", "validateAndFormatPhoneNumber:com.example.sms_app.utils", "hasValidVietnamesePrefix:com.example.sms_app.utils", "isValidVietnameseNumber:com.example.sms_app.utils", "sendSmsToVietnameseNumber:com.example.sms_app.utils", "initialize:com.example.sms_app.utils", "sendSmsUsingReflection:com.example.sms_app.utils"], "src\\main\\java\\com\\example\\sms_app\\api\\UpdateApiService.kt": ["VersionResponse:com.example.sms_app.api", "version:com.example.sms_app.api.VersionResponse", "downloadUrl:com.example.sms_app.api.VersionResponse", "getLatestVersion:com.example.sms_app.api.UpdateApiService", "versionCode:com.example.sms_app.api.VersionResponse", "UpdateApiService:com.example.sms_app.api", "forceUpdate:com.example.sms_app.api.VersionResponse", "downloadApk:com.example.sms_app.api.UpdateApiService", "fileSize:com.example.sms_app.api.VersionResponse", "releaseNotes:com.example.sms_app.api.VersionResponse"], "src\\main\\java\\com\\example\\sms_app\\presentation\\screen\\MainScreen.kt": ["MainScreen:com.example.sms_app.presentation.screen"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\service\\HiddenSmsReceiver_GeneratedInjector.java": ["HiddenSmsReceiver_GeneratedInjector:com.example.sms_app.service", "injectHiddenSmsReceiver:com.example.sms_app.service.HiddenSmsReceiver_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\SendMessageViewModel_Factory.java": ["<init>:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_Factory", "get:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_Factory", "create:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_Factory", "SendMessageViewModel_Factory:com.example.sms_app.presentation.viewmodel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\SmsApplication_GeneratedInjector.java": ["injectSmsApplication:com.example.sms_app.presentation.SmsApplication_GeneratedInjector", "SmsApplication_GeneratedInjector:com.example.sms_app.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\activity\\Hilt_MainActivity.java": ["inject:com.example.sms_app.presentation.activity.Hilt_MainActivity", "componentManager:com.example.sms_app.presentation.activity.Hilt_MainActivity", "Hilt_MainActivity:com.example.sms_app.presentation.activity", "<init>:com.example.sms_app.presentation.activity.Hilt_MainActivity", "createComponentManager:com.example.sms_app.presentation.activity.Hilt_MainActivity", "generatedComponent:com.example.sms_app.presentation.activity.Hilt_MainActivity", "getDefaultViewModelProviderFactory:com.example.sms_app.presentation.activity.Hilt_MainActivity"], "src\\main\\java\\com\\example\\sms_app\\service\\HiddenSmsReceiver.kt": ["HiddenSmsReceiver:com.example.sms_app.service", "<init>:com.example.sms_app.service.HiddenSmsReceiver", "onReceive:com.example.sms_app.service.HiddenSmsReceiver"], "src\\main\\java\\com\\example\\sms_app\\utils\\SimManager.kt": ["getSimDisplayName:com.example.sms_app.utils.SimManager", "carrierName:com.example.sms_app.utils.SimInfo", "getSimForCustomer:com.example.sms_app.utils.SimConfig", "displayName:com.example.sms_app.utils.SimInfo", "hasMultipleSims:com.example.sms_app.utils.SimManager", "SimManager:com.example.sms_app.utils", "secondarySim:com.example.sms_app.utils.SimConfig", "allSims:com.example.sms_app.utils.SimConfig", "getAvailableSims:com.example.sms_app.utils.SimManager", "phoneNumber:com.example.sms_app.utils.SimInfo", "SimConfig:com.example.sms_app.utils", "SimInfo:com.example.sms_app.utils", "simSlotIndex:com.example.sms_app.utils.SimInfo", "isDualSim:com.example.sms_app.utils.SimConfig", "primarySim:com.example.sms_app.utils.SimConfig", "<init>:com.example.sms_app.utils.SimManager", "subscriptionId:com.example.sms_app.utils.SimInfo"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\PatternDialog.kt": ["PatternDialog:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\service\\SmsService.kt": ["EXTRA_MESSAGE:com.example.sms_app.service.SmsService.Companion", "EXTRA_MAX_RETRY:com.example.sms_app.service.SmsService.Companion", "Companion:com.example.sms_app.service.SmsService", "SmsAttempt:com.example.sms_app.service.SmsService", "ACTION_PROGRESS_UPDATE:com.example.sms_app.service.SmsService.Companion", "onStartCommand:com.example.sms_app.service.SmsService", "EXTRA_TEMPLATE_ID:com.example.sms_app.service.SmsService.Companion", "phoneNumber:com.example.sms_app.service.SmsService.SmsAttempt", "EXTRA_TOTAL:com.example.sms_app.service.SmsService.Companion", "customer:com.example.sms_app.service.SmsService.SmsAttempt", "maxAttempts:com.example.sms_app.service.SmsService.SmsAttempt", "managerIndex:com.example.sms_app.service.SmsService.SmsAttempt", "attemptNumber:com.example.sms_app.service.SmsService.SmsAttempt", "EXTRA_PROGRESS:com.example.sms_app.service.SmsService.Companion", "SmsService:com.example.sms_app.service", "phoneFormat:com.example.sms_app.service.SmsService.SmsAttempt", "ACTION_SMS_COMPLETED:com.example.sms_app.service.SmsService.Companion", "ACTION_SMS_COUNT_UPDATED:com.example.sms_app.service.SmsService.Companion", "EXTRA_RETRY_DELAY:com.example.sms_app.service.SmsService.Companion", "requestId:com.example.sms_app.service.SmsService.SmsAttempt", "onBind:com.example.sms_app.service.SmsService", "ACTION_CUSTOMER_DELETED:com.example.sms_app.service.SmsService.Companion", "EXTRA_SMS_COUNT:com.example.sms_app.service.SmsService.Companion", "onCreate:com.example.sms_app.service.SmsService", "smsManager:com.example.sms_app.service.SmsService.SmsAttempt", "formatIndex:com.example.sms_app.service.SmsService.SmsAttempt", "SMS_DELIVERED_ACTION:com.example.sms_app.service.SmsService.Companion", "EXTRA_INTERVAL_SECONDS:com.example.sms_app.service.SmsService.Companion", "<init>:com.example.sms_app.service.SmsService", "EXTRA_CUSTOMER_ID:com.example.sms_app.service.SmsService.Companion", "CHANNEL_ID:com.example.sms_app.service.SmsService.Companion", "NOTIFICATION_CHANNEL_ID:com.example.sms_app.service.SmsService.Companion", "SMS_SENT_ACTION:com.example.sms_app.service.SmsService.Companion", "continuation:com.example.sms_app.service.SmsService.SmsAttempt", "NOTIFICATION_ID:com.example.sms_app.service.SmsService.Companion", "message:com.example.sms_app.service.SmsService.SmsAttempt", "EXTRA_SIM_ID:com.example.sms_app.service.SmsService.Companion", "<init>:com.example.sms_app.service.SmsService.Companion", "onDestroy:com.example.sms_app.service.SmsService"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\SettingViewModel_HiltModules.java": ["BindsModule:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules", "provide:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules.KeyModule", "binds:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules.BindsModule", "SettingViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "KeyModule:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\SendMessageViewModel.kt": ["SendMessageViewModel:com.example.sms_app.presentation.viewmodel", "millisUntilFinished:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "resetAllStates:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "completion:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "progress:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "sendMessage:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "Companion:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "isSending:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "onCleared:com.example.sms_app.presentation.viewmodel.SendMessageViewModel", "<init>:com.example.sms_app.presentation.viewmodel.SendMessageViewModel.Companion", "stop:com.example.sms_app.presentation.viewmodel.SendMessageViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_KeyModule.java": ["_com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_KeyModule.java": ["_com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_BindsModule.java": ["_com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\example\\sms_app\\utils\\CryptoUtils.kt": ["CryptoUtils:com.example.sms_app.utils", "encrypt:com.example.sms_app.utils.CryptoUtils", "<init>:com.example.sms_app.utils.CryptoUtils", "decrypt:com.example.sms_app.utils.CryptoUtils"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\PatternViewModel_HiltModules_KeyModule_ProvideFactory.java": ["get:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules_KeyModule_ProvideFactory", "PatternViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "<init>:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\MainViewModel_HiltModules.java": ["provide:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules.KeyModule", "KeyModule:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules", "binds:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules.BindsModule", "MainViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "BindsModule:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\MyBottomBar.kt": ["Message:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.Search", "None:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.Setting", "Search:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.SendMessage", "SendMessage:com.example.sms_app.presentation.component.BottomButton", "Setting:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.None", "BottomButton:com.example.sms_app.presentation.component", "Send:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.Message", "MoreVert:com.example.sms_app.presentation.component.BottomButton", "WrongPattern:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.WrongPattern", "<init>:com.example.sms_app.presentation.component.BottomButton.MoreVert", "MyBottomBar:com.example.sms_app.presentation.component", "Add:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.Send", "icon:com.example.sms_app.presentation.component.BottomButton", "<init>:com.example.sms_app.presentation.component.BottomButton.Add"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\service\\Hilt_SmsService.java": ["onCreate:com.example.sms_app.service.Hilt_SmsService", "createComponentManager:com.example.sms_app.service.Hilt_SmsService", "<init>:com.example.sms_app.service.Hilt_SmsService", "componentManager:com.example.sms_app.service.Hilt_SmsService", "inject:com.example.sms_app.service.Hilt_SmsService", "generatedComponent:com.example.sms_app.service.Hilt_SmsService", "Hilt_SmsService:com.example.sms_app.service"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\SettingDialog.kt": ["text:com.example.sms_app.presentation.component.SwitchSetting", "Limit:com.example.sms_app.presentation.component.SwitchSetting", "Info:com.example.sms_app.presentation.component.TextSetting", "default:com.example.sms_app.presentation.component.TextSetting", "SettingDialog:com.example.sms_app.presentation.component", "<init>:com.example.sms_app.presentation.component.TextSetting.Info", "<init>:com.example.sms_app.presentation.component.NumSetting.Limit", "<init>:com.example.sms_app.presentation.component.TextSetting.Permission", "default:com.example.sms_app.presentation.component.SwitchSetting", "default:com.example.sms_app.presentation.component.NumSetting", "<init>:com.example.sms_app.presentation.component.SwitchSetting.Update", "Delay:com.example.sms_app.presentation.component.NumSetting", "Activation:com.example.sms_app.presentation.component.TextSetting", "text:com.example.sms_app.presentation.component.NumSetting", "text:com.example.sms_app.presentation.component.TextSetting", "Limit:com.example.sms_app.presentation.component.NumSetting", "Update:com.example.sms_app.presentation.component.SwitchSetting", "<init>:com.example.sms_app.presentation.component.NumSetting.Delay", "<init>:com.example.sms_app.presentation.component.SwitchSetting.Limit", "SwitchSetting:com.example.sms_app.presentation.component", "<init>:com.example.sms_app.presentation.component.TextSetting.Activation", "Permission:com.example.sms_app.presentation.component.TextSetting", "EditValueDialog:com.example.sms_app.presentation.component", "getValue:com.example.sms_app.presentation.component.NumSetting", "getValue:com.example.sms_app.presentation.component.SwitchSetting", "TextSetting:com.example.sms_app.presentation.component", "NumSetting:com.example.sms_app.presentation.component"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\BackUpViewModel_Factory.java": ["<init>:com.example.sms_app.presentation.viewmodel.BackUpViewModel_Factory", "BackUpViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.BackUpViewModel_Factory", "get:com.example.sms_app.presentation.viewmodel.BackUpViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.BackUpViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_KeyModule", "_com_example_sms_app_presentation_viewmodel_AddCustomerViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\presentation\\SmsApplication.kt": ["<init>:com.example.sms_app.presentation.SmsApplication", "onCreate:com.example.sms_app.presentation.SmsApplication", "SmsApplication:com.example.sms_app.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\activity\\MainActivity_GeneratedInjector.java": ["injectMainActivity:com.example.sms_app.presentation.activity.MainActivity_GeneratedInjector", "MainActivity_GeneratedInjector:com.example.sms_app.presentation.activity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory.java": ["AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "<init>:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\BackUpViewModel_HiltModules.java": ["BackUpViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "binds:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules.BindsModule", "provide:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules.KeyModule", "BindsModule:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules", "KeyModule:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\SearchDialog.kt": ["SearchDialog:com.example.sms_app.presentation.component"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_KeyModule", "_com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\AddCustomerViewModel_HiltModules.java": ["provide:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules.KeyModule", "KeyModule:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules", "binds:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules.BindsModule", "BindsModule:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_HiltModules", "AddCustomerViewModel_HiltModules:com.example.sms_app.presentation.viewmodel"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\MainViewModel.kt": ["customers:com.example.sms_app.presentation.viewmodel.MainViewModel", "searchCustomers:com.example.sms_app.presentation.viewmodel.MainViewModel", "removeDuplicatesByPhoneNumber:com.example.sms_app.presentation.viewmodel.MainViewModel", "delete:com.example.sms_app.presentation.viewmodel.MainViewModel", "getDefaultTemplate:com.example.sms_app.presentation.viewmodel.MainViewModel", "getMessageTemplates:com.example.sms_app.presentation.viewmodel.MainViewModel", "handleExcelFile:com.example.sms_app.presentation.viewmodel.MainViewModel", "MainViewModel:com.example.sms_app.presentation.viewmodel", "unselectAll:com.example.sms_app.presentation.viewmodel.MainViewModel", "selectAll:com.example.sms_app.presentation.viewmodel.MainViewModel", "select:com.example.sms_app.presentation.viewmodel.MainViewModel", "deleteAll:com.example.sms_app.presentation.viewmodel.MainViewModel", "updateCustomers:com.example.sms_app.presentation.viewmodel.MainViewModel", "sync:com.example.sms_app.presentation.viewmodel.MainViewModel", "clearSearch:com.example.sms_app.presentation.viewmodel.MainViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\service\\Hilt_HiddenSmsReceiver.java": ["onReceive:com.example.sms_app.service.Hilt_HiddenSmsReceiver", "inject:com.example.sms_app.service.Hilt_HiddenSmsReceiver", "<init>:com.example.sms_app.service.Hilt_HiddenSmsReceiver", "Hilt_HiddenSmsReceiver:com.example.sms_app.service"], "build\\generated\\source\\buildConfig\\debug\\com\\example\\sms_app\\BuildConfig.java": ["BuildConfig:com.example.sms_app", "APP_SIGNATURE:com.example.sms_app.BuildConfig", "ENABLE_TAMPER_DETECTION:com.example.sms_app.BuildConfig", "BUILD_TYPE:com.example.sms_app.BuildConfig", "ENABLE_INTEGRITY_CHECK:com.example.sms_app.BuildConfig", "APPLICATION_ID:com.example.sms_app.BuildConfig", "VERSION_CODE:com.example.sms_app.BuildConfig", "<init>:com.example.sms_app.BuildConfig", "VERSION_NAME:com.example.sms_app.BuildConfig", "BUILD_DATE:com.example.sms_app.BuildConfig", "DEBUG:com.example.sms_app.BuildConfig"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\SelectSimDialog.kt": ["SelectSimDialog:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\MoreView.kt": ["Out:com.example.sms_app.presentation.component.MoreVertFunctions", "MoreVertFunctions:com.example.sms_app.presentation.component", "<init>:com.example.sms_app.presentation.component.MoreVertFunctions.Home", "icon:com.example.sms_app.presentation.component.MoreVertFunctions", "<init>:com.example.sms_app.presentation.component.MoreVertFunctions.Out", "<init>:com.example.sms_app.presentation.component.MoreVertFunctions.Filter", "Update:com.example.sms_app.presentation.component.MoreVertFunctions", "text:com.example.sms_app.presentation.component.MoreVertFunctions", "Filter:com.example.sms_app.presentation.component.MoreVertFunctions", "MoreView:com.example.sms_app.presentation.component", "<init>:com.example.sms_app.presentation.component.MoreVertFunctions.Update", "Support:com.example.sms_app.presentation.component.MoreVertFunctions", "<init>:com.example.sms_app.presentation.component.MoreVertFunctions.Support", "Home:com.example.sms_app.presentation.component.MoreVertFunctions"], "src\\main\\java\\com\\example\\sms_app\\presentation\\theme\\Theme.kt": ["SmsAppTheme:com.example.sms_app.presentation.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\BackUpViewModel_HiltModules_KeyModule_ProvideFactory.java": ["provide:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules_KeyModule_ProvideFactory", "BackUpViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "get:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.BackUpViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\example\\sms_app\\ui\\TemplateSelectionDialog.kt": ["TemplateSelectionDialog:com.example.sms_app.ui", "TemplateSelectionItem:com.example.sms_app.ui"], "src\\main\\java\\com\\example\\sms_app\\ui\\TemplateConfigDialog.kt": ["TemplateConfigDialog:com.example.sms_app.ui"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\FolderSelectionDialog.kt": ["path:com.example.sms_app.presentation.component.FileItem", "size:com.example.sms_app.presentation.component.FileItem", "FileItem:com.example.sms_app.presentation.component", "name:com.example.sms_app.presentation.component.FileItem", "lastModified:com.example.sms_app.presentation.component.FileItem", "FolderSelectionDialog:com.example.sms_app.presentation.component", "isDirectory:com.example.sms_app.presentation.component.FileItem"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\SendMessageViewModel_HiltModules.java": ["SendMessageViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "BindsModule:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules", "binds:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules.BindsModule", "provide:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules.KeyModule", "KeyModule:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\PatternViewModel_HiltModules.java": ["BindsModule:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules", "KeyModule:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules", "PatternViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "provide:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules.KeyModule", "binds:com.example.sms_app.presentation.viewmodel.PatternViewModel_HiltModules.BindsModule"], "src\\main\\java\\com\\example\\sms_app\\data\\SmsProgress.kt": ["progress:com.example.sms_app.data.SmsProgress", "message:com.example.sms_app.data.SmsProgress", "total:com.example.sms_app.data.SmsProgress", "SmsProgress:com.example.sms_app.data"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\MyTopBar.kt": ["MyTopBar:com.example.sms_app.presentation.component"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_BindsModule", "_com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_BindsModule", "_com_example_sms_app_presentation_viewmodel_UpdateViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\SettingViewModel.kt": ["appSettings:com.example.sms_app.presentation.viewmodel.SettingViewModel", "saveRetry:com.example.sms_app.presentation.viewmodel.SettingViewModel", "sync:com.example.sms_app.presentation.viewmodel.SettingViewModel", "saveDelay:com.example.sms_app.presentation.viewmodel.SettingViewModel", "saveBool:com.example.sms_app.presentation.viewmodel.SettingViewModel", "edit:com.example.sms_app.presentation.viewmodel.SettingViewModel", "saveNumber:com.example.sms_app.presentation.viewmodel.SettingViewModel", "saveCustomerLimit:com.example.sms_app.presentation.viewmodel.SettingViewModel", "SettingViewModel:com.example.sms_app.presentation.viewmodel"], "src\\main\\java\\com\\example\\sms_app\\utils\\ExcelImporter.kt": ["ExcelImporter:com.example.sms_app.utils", "importCustomers:com.example.sms_app.utils.ExcelImporter", "cleanPhoneNumbers:com.example.sms_app.utils.ExcelImporter", "testDataCleaning:com.example.sms_app.utils.ExcelImporter", "testPhoneNumberCleaning:com.example.sms_app.utils.ExcelImporter", "cleanPhoneNumber:com.example.sms_app.utils.ExcelImporter"], "src\\main\\java\\com\\example\\sms_app\\data\\SmsRepository.kt": ["getAppSettings:com.example.sms_app.data.SmsRepository", "incrementSmsCount:com.example.sms_app.data.SmsRepository", "isDualSimEnabled:com.example.sms_app.data.SmsRepository", "getSelectedSim:com.example.sms_app.data.SmsRepository", "getCountdownStartTime:com.example.sms_app.data.SmsRepository", "saveTemplates:com.example.sms_app.data.SmsRepository", "getDualSimIds:com.example.sms_app.data.SmsRepository", "setSelectedSim:com.example.sms_app.data.SmsRepository", "setDefaultTemplate:com.example.sms_app.data.SmsRepository", "getConfig:com.example.sms_app.data.SmsRepository", "shouldSendParallelToDualSim:com.example.sms_app.data.SmsRepository", "getCountdownTotalTime:com.example.sms_app.data.SmsRepository", "getMessageTemplates:com.example.sms_app.data.SmsRepository", "getCustomers:com.example.sms_app.data.SmsRepository", "getSimForCustomer:com.example.sms_app.data.SmsRepository", "canSendMoreSms:com.example.sms_app.data.SmsRepository", "MAX_SMS_PER_DAY:com.example.sms_app.data.SmsRepository.Companion", "debugPrintAllSmsCountValues:com.example.sms_app.data.SmsRepository", "resetAllSimCounts:com.example.sms_app.data.SmsRepository", "saveCountdownData:com.example.sms_app.data.SmsRepository", "forceRefreshSmsCount:com.example.sms_app.data.SmsRepository", "saveMessageTemplates:com.example.sms_app.data.SmsRepository", "setDualSimConfig:com.example.sms_app.data.SmsRepository", "getCountdownCustomerCount:com.example.sms_app.data.SmsRepository", "clearCountdownData:com.example.sms_app.data.SmsRepository", "getSmsCountToday:com.example.sms_app.data.SmsRepository", "setManualSmsCount:com.example.sms_app.data.SmsRepository", "Companion:com.example.sms_app.data.SmsRepository", "getDefaultTemplate:com.example.sms_app.data.SmsRepository", "getTemplates:com.example.sms_app.data.SmsRepository", "saveCustomers:com.example.sms_app.data.SmsRepository", "saveAppSettings:com.example.sms_app.data.SmsRepository", "resetSmsCount:com.example.sms_app.data.SmsRepository", "SmsRepository:com.example.sms_app.data", "saveConfig:com.example.sms_app.data.SmsRepository", "<init>:com.example.sms_app.data.SmsRepository.Companion"], "src\\main\\java\\com\\example\\sms_app\\di\\AppModule.kt": ["provideContext:com.example.sms_app.di.AppModule", "AppModule:com.example.sms_app.di", "<init>:com.example.sms_app.di.AppModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\SettingViewModel_Factory.java": ["get:com.example.sms_app.presentation.viewmodel.SettingViewModel_Factory", "SettingViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.SettingViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.SettingViewModel_Factory", "<init>:com.example.sms_app.presentation.viewmodel.SettingViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_SmsApplication_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_SmsApplication_GeneratedInjector", "_com_example_sms_app_presentation_SmsApplication_GeneratedInjector:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\utils\\ContextUtil.kt": ["getAppVersion:com.example.sms_app.utils"], "src\\main\\java\\com\\example\\sms_app\\ui\\SmsPreviewDialog.kt": ["SmsPreviewDialog:com.example.sms_app.ui"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_BindsModule", "_com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\UpdateViewModel_HiltModules.java": ["BindsModule:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules", "KeyModule:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules", "UpdateViewModel_HiltModules:com.example.sms_app.presentation.viewmodel", "binds:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules.BindsModule", "provide:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\UpdateDialog.kt": ["UpdateDialog:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\data\\SessionBackup.kt": ["completedCustomers:com.example.sms_app.data.SmsSession", "SessionBackup:com.example.sms_app.data", "sessionId:com.example.sms_app.data.SmsSession", "sessionName:com.example.sms_app.data.SmsSession", "hasActiveSession:com.example.sms_app.data.SessionBackup", "saveActiveSession:com.example.sms_app.data.SessionBackup", "updateSessionProgress:com.example.sms_app.data.SessionBackup", "clearActiveSession:com.example.sms_app.data.SessionBackup", "lastUpdateTime:com.example.sms_app.data.SmsSession", "markSessionFailed:com.example.sms_app.data.SessionBackup", "templateId:com.example.sms_app.data.SmsSession", "deleteSessionFromHistory:com.example.sms_app.data.SessionBackup", "startTime:com.example.sms_app.data.SmsSession", "failedCustomerId:com.example.sms_app.data.SmsSession", "markCustomerProcessed:com.example.sms_app.data.SessionBackup", "getSessionHistory:com.example.sms_app.data.SessionBackup", "getSessionSummary:com.example.sms_app.data.SessionBackup", "restoreCustomersFromSession:com.example.sms_app.data.SessionBackup", "failedReason:com.example.sms_app.data.SmsSession", "totalCustomers:com.example.sms_app.data.SmsSession", "status:com.example.sms_app.data.SmsSession", "clearAllSessionHistory:com.example.sms_app.data.SessionBackup", "remainingCustomers:com.example.sms_app.data.SmsSession", "SmsSession:com.example.sms_app.data", "completeSession:com.example.sms_app.data.SessionBackup", "generateSessionId:com.example.sms_app.data.SessionBackup", "updateSessionTime:com.example.sms_app.data.SessionBackup", "sentCount:com.example.sms_app.data.SmsSession", "getActiveSession:com.example.sms_app.data.SessionBackup"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\AddCustomerDialog.kt": ["<init>:com.example.sms_app.presentation.component.CustomerField.Pattern", "Id:com.example.sms_app.presentation.component.CustomerField", "PhoneNumber:com.example.sms_app.presentation.component.CustomerField", "Pattern:com.example.sms_app.presentation.component.CustomerField", "default:com.example.sms_app.presentation.component.CustomerField", "AddCustomerDialog:com.example.sms_app.presentation.component", "Option4:com.example.sms_app.presentation.component.CustomerField", "Option2:com.example.sms_app.presentation.component.CustomerField", "<init>:com.example.sms_app.presentation.component.CustomerField.Name", "<init>:com.example.sms_app.presentation.component.CustomerField.Option1", "placeholder:com.example.sms_app.presentation.component.CustomerField", "<init>:com.example.sms_app.presentation.component.CustomerField.PhoneNumber", "<init>:com.example.sms_app.presentation.component.CustomerField.Address", "Name:com.example.sms_app.presentation.component.CustomerField", "<init>:com.example.sms_app.presentation.component.CustomerField.Option5", "<init>:com.example.sms_app.presentation.component.CustomerField.Option4", "CustomerField:com.example.sms_app.presentation.component", "keyboardType:com.example.sms_app.presentation.component.CustomerField", "<init>:com.example.sms_app.presentation.component.CustomerField.Option3", "<init>:com.example.sms_app.presentation.component.CustomerField.Option2", "Option5:com.example.sms_app.presentation.component.CustomerField", "Option3:com.example.sms_app.presentation.component.CustomerField", "<init>:com.example.sms_app.presentation.component.CustomerField.Id", "Option1:com.example.sms_app.presentation.component.CustomerField", "icon:com.example.sms_app.presentation.component.CustomerField", "Address:com.example.sms_app.presentation.component.CustomerField"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\service\\SmsService_GeneratedInjector.java": ["SmsService_GeneratedInjector:com.example.sms_app.service", "injectSmsService:com.example.sms_app.service.SmsService_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\AddCustomerViewModel_Factory.java": ["AddCustomerViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_Factory", "<init>:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_Factory", "get:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_service_SmsService_GeneratedInjector.java": ["_com_example_sms_app_service_SmsService_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_service_SmsService_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_KeyModule.java": ["_com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_PatternViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\example\\sms_app\\ui\\components\\SplashScreen.kt": ["SplashScreen:com.example.sms_app.ui.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_BindsModule.java": ["_com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_BackUpViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\example\\sms_app\\ui\\components\\AboutScreen.kt": ["AboutScreen:com.example.sms_app.ui.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\SettingViewModel_HiltModules_KeyModule_ProvideFactory.java": ["SettingViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "<init>:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.SettingViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_activity_MainActivity_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_activity_MainActivity_GeneratedInjector", "_com_example_sms_app_presentation_activity_MainActivity_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\MainViewModel_Factory.java": ["get:com.example.sms_app.presentation.viewmodel.MainViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.MainViewModel_Factory", "<init>:com.example.sms_app.presentation.viewmodel.MainViewModel_Factory", "MainViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.MainViewModel_Factory"], "src\\main\\java\\com\\example\\sms_app\\utils\\AutoSmsDisabler.kt": ["generateSecurityReport:com.example.sms_app.utils.AutoSmsDisabler", "isSecure:com.example.sms_app.utils.SecurityReport", "initializeSafeMode:com.example.sms_app.utils.AutoSmsDisabler", "<init>:com.example.sms_app.utils.AutoSmsDisabler", "warnings:com.example.sms_app.utils.SecurityReport", "AutoSmsDisabler:com.example.sms_app.utils", "disableAllAutoSmsFeatures:com.example.sms_app.utils.AutoSmsDisabler", "checkSecurityStatus:com.example.sms_app.utils.AutoSmsDisabler", "issues:com.example.sms_app.utils.SecurityReport", "info:com.example.sms_app.utils.SecurityReport", "SecurityReport:com.example.sms_app.utils"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_example_sms_app_presentation_SmsApplication.java": ["<init>:dagger.hilt.internal.aggregatedroot.codegen._com_example_sms_app_presentation_SmsApplication", "_com_example_sms_app_presentation_SmsApplication:dagger.hilt.internal.aggregatedroot.codegen"], "src\\main\\java\\com\\example\\sms_app\\utils\\AppUpdateManager.kt": ["versionCode:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "checkForUpdates:com.example.sms_app.utils.AppUpdateManager", "testApiConnection:com.example.sms_app.utils.AppUpdateManager", "getCurrentVersionName:com.example.sms_app.utils.AppUpdateManager", "Companion:com.example.sms_app.utils.AppUpdateManager", "releaseNotes:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "fileSize:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "minSupportedVersion:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "versionName:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "AppUpdateManager:com.example.sms_app.utils", "openUninstallSettings:com.example.sms_app.utils.AppUpdateManager", "canInstallApk:com.example.sms_app.utils.AppUpdateManager", "forceUpdate:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "<init>:com.example.sms_app.utils.AppUpdateManager.Companion", "UpdateInfo:com.example.sms_app.utils.AppUpdateManager", "downloadUrl:com.example.sms_app.utils.AppUpdateManager.UpdateInfo", "downloadAndInstallUpdate:com.example.sms_app.utils.AppUpdateManager", "openInstallPermissionSettings:com.example.sms_app.utils.AppUpdateManager"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\PatternViewModel_Factory.java": ["PatternViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.PatternViewModel_Factory", "get:com.example.sms_app.presentation.viewmodel.PatternViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.PatternViewModel_Factory", "<init>:com.example.sms_app.presentation.viewmodel.PatternViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\utils\\AppUpdateManager_Factory.java": ["create:com.example.sms_app.utils.AppUpdateManager_Factory", "AppUpdateManager_Factory:com.example.sms_app.utils", "<init>:com.example.sms_app.utils.AppUpdateManager_Factory", "get:com.example.sms_app.utils.AppUpdateManager_Factory", "newInstance:com.example.sms_app.utils.AppUpdateManager_Factory"], "src\\main\\java\\com\\example\\sms_app\\ui\\SettingsDialog.kt": ["SettingsDialog:com.example.sms_app.ui"], "src\\main\\java\\com\\example\\sms_app\\ui\\AddCustomerDialog.kt": ["AddCustomerDialog:com.example.sms_app.ui"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\SendMessageViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules_KeyModule_ProvideFactory", "SendMessageViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "<init>:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.SendMessageViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_KeyModule.java": ["_com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_SendMessageViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\example\\sms_app\\data\\TemplateManager.kt": ["<init>:com.example.sms_app.data.TemplateManager", "id:com.example.sms_app.data.MessageTemplate", "TemplateManager:com.example.sms_app.data", "MessageTemplate:com.example.sms_app.data", "description:com.example.sms_app.data.MessageTemplate", "getDefaultTemplates:com.example.sms_app.data.TemplateManager", "getTemplateById:com.example.sms_app.data.TemplateManager", "content:com.example.sms_app.data.MessageTemplate"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\data\\SmsRepository_Factory.java": ["SmsRepository_Factory:com.example.sms_app.data", "<init>:com.example.sms_app.data.SmsRepository_Factory", "create:com.example.sms_app.data.SmsRepository_Factory", "get:com.example.sms_app.data.SmsRepository_Factory", "newInstance:com.example.sms_app.data.SmsRepository_Factory"], "src\\main\\java\\com\\example\\sms_app\\data\\AppSettings.kt": ["customerLimit:com.example.sms_app.data.AppSettings", "AppSettings:com.example.sms_app.data", "enableSound:com.example.sms_app.data.AppSettings", "clearCacheAfterImport:com.example.sms_app.data.AppSettings", "intervalBetweenSmsSeconds:com.example.sms_app.data.AppSettings", "maxRetryAttempts:com.example.sms_app.data.AppSettings", "retryDelaySeconds:com.example.sms_app.data.AppSettings", "useRandomSpacing:com.example.sms_app.data.AppSettings", "enableVibrate:com.example.sms_app.data.AppSettings", "maxIntervalSeconds:com.example.sms_app.data.AppSettings", "enableFilter:com.example.sms_app.data.AppSettings", "randomizeContent:com.example.sms_app.data.AppSettings", "minIntervalSeconds:com.example.sms_app.data.AppSettings", "randomizeInterval:com.example.sms_app.data.AppSettings", "enableAutoSms:com.example.sms_app.data.AppSettings", "isLimitCustomer:com.example.sms_app.data.AppSettings", "enableUpdate:com.example.sms_app.data.AppSettings", "addRandomEmoji:com.example.sms_app.data.AppSettings", "isRandomNumber:com.example.sms_app.data.AppSettings"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\BackUpViewModel.kt": ["backUp:com.example.sms_app.presentation.viewmodel.BackUpViewModel", "BackUpViewModel:com.example.sms_app.presentation.viewmodel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_BindsModule.java": ["_com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_SettingViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\presentation\\viewmodel\\UpdateViewModel_Factory.java": ["<init>:com.example.sms_app.presentation.viewmodel.UpdateViewModel_Factory", "UpdateViewModel_Factory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.UpdateViewModel_Factory", "newInstance:com.example.sms_app.presentation.viewmodel.UpdateViewModel_Factory", "get:com.example.sms_app.presentation.viewmodel.UpdateViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_KeyModule", "_com_example_sms_app_presentation_viewmodel_MainViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\SendMessageDialog.kt": ["SendMessageDialog:com.example.sms_app.presentation.component", "formatDuration:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\presentation\\viewmodel\\AddCustomerViewModel.kt": ["AddCustomerViewModel:com.example.sms_app.presentation.viewmodel", "verify:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel", "getValue:com.example.sms_app.presentation.viewmodel.AddCustomerViewModel"], "src\\main\\java\\com\\example\\sms_app\\data\\SmsTemplate.kt": ["name:com.example.sms_app.data.SmsTemplate", "phoneNumber:com.example.sms_app.data.SmsConfig", "template:com.example.sms_app.data.SmsConfig", "content:com.example.sms_app.data.SmsTemplate", "id:com.example.sms_app.data.SmsTemplate", "variableValues:com.example.sms_app.data.SmsConfig", "isActive:com.example.sms_app.data.SmsConfig", "SmsTemplate:com.example.sms_app.data", "variables:com.example.sms_app.data.SmsTemplate", "replaceVariables:com.example.sms_app.data.SmsTemplate", "SmsConfig:com.example.sms_app.data", "intervalSeconds:com.example.sms_app.data.SmsConfig"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_example_sms_app_service_HiddenSmsReceiver_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_service_HiddenSmsReceiver_GeneratedInjector", "_com_example_sms_app_service_HiddenSmsReceiver_GeneratedInjector:hilt_aggregated_deps"], "src\\main\\java\\com\\example\\sms_app\\utils\\SecurityUtils.kt": ["performTamperResponses:com.example.sms_app.utils.SecurityUtils", "verifyAppIntegrity:com.example.sms_app.utils.SecurityUtils", "SecurityUtils:com.example.sms_app.utils", "verifyPlayIntegrity:com.example.sms_app.utils.SecurityUtils", "<init>:com.example.sms_app.utils.SecurityUtils"], "src\\main\\java\\com\\example\\sms_app\\presentation\\theme\\Color.kt": ["Pink80:com.example.sms_app.presentation.theme", "Purple80:com.example.sms_app.presentation.theme", "PurpleGrey40:com.example.sms_app.presentation.theme", "Pink40:com.example.sms_app.presentation.theme", "Purple40:com.example.sms_app.presentation.theme", "PurpleGrey80:com.example.sms_app.presentation.theme"], "src\\main\\java\\com\\example\\sms_app\\presentation\\component\\BackUpDialog.kt": ["BackUpDialog:com.example.sms_app.presentation.component"], "src\\main\\java\\com\\example\\sms_app\\data\\Customer.kt": ["Companion:com.example.sms_app.data.Customer", "detectCarrier:com.example.sms_app.data.Customer.Companion", "address:com.example.sms_app.data.Customer", "option5:com.example.sms_app.data.Customer", "phoneNumber:com.example.sms_app.data.Customer", "option4:com.example.sms_app.data.Customer", "getPersonalizedMessage:com.example.sms_app.data.Customer", "Customer:com.example.sms_app.data", "idNumber:com.example.sms_app.data.Customer", "id:com.example.sms_app.data.Customer", "<init>:com.example.sms_app.data.Customer.Companion", "option3:com.example.sms_app.data.Customer", "carrier:com.example.sms_app.data.Customer", "option2:com.example.sms_app.data.Customer", "name:com.example.sms_app.data.Customer", "option1:com.example.sms_app.data.Customer", "isSelected:com.example.sms_app.data.Customer", "templateNumber:com.example.sms_app.data.Customer"], "src\\main\\java\\com\\example\\sms_app\\ui\\CustomerDetailDialog.kt": ["CustomerDetailDialog:com.example.sms_app.ui"], "src\\main\\java\\com\\example\\sms_app\\presentation\\PermissionChecker.kt": ["PermissionChecker:com.example.sms_app.presentation"], "src\\main\\java\\com\\example\\sms_app\\presentation\\activity\\MainActivity.kt": ["<init>:com.example.sms_app.presentation.activity.MainActivity", "MainActivity:com.example.sms_app.presentation.activity", "onCreate:com.example.sms_app.presentation.activity.MainActivity"], "src\\main\\java\\com\\example\\sms_app\\utils\\IntentUtils.kt": ["openBrowser:com.example.sms_app.utils.IntentUtils", "openFacebook:com.example.sms_app.utils.IntentUtils", "IntentUtils:com.example.sms_app.utils", "openEmail:com.example.sms_app.utils.IntentUtils", "openZalo:com.example.sms_app.utils.IntentUtils", "<init>:com.example.sms_app.utils.IntentUtils"], "src\\main\\java\\com\\example\\sms_app\\ui\\ConfirmationDialogs.kt": ["RestoreUnsentCustomersDialog:com.example.sms_app.ui", "RemoveDuplicatesConfirmDialog:com.example.sms_app.ui"], "src\\main\\java\\com\\example\\sms_app\\ui\\components\\AppLogoScreen.kt": ["AppLogoScreen:com.example.sms_app.ui.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\MainViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory", "MainViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "create:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.example.sms_app.presentation.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_example_sms_app_di_AppModule.java": ["<init>:hilt_aggregated_deps._com_example_sms_app_di_AppModule", "_com_example_sms_app_di_AppModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\example\\sms_app\\di\\AppModule_ProvideContextFactory.java": ["provideContext:com.example.sms_app.di.AppModule_ProvideContextFactory", "get:com.example.sms_app.di.AppModule_ProvideContextFactory", "create:com.example.sms_app.di.AppModule_ProvideContextFactory", "AppModule_ProvideContextFactory:com.example.sms_app.di", "<init>:com.example.sms_app.di.AppModule_ProvideContextFactory"], "src\\main\\java\\com\\example\\sms_app\\presentation\\theme\\Type.kt": ["Typography:com.example.sms_app.presentation.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\example\\sms_app\\presentation\\viewmodel\\UpdateViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "UpdateViewModel_HiltModules_KeyModule_ProvideFactory:com.example.sms_app.presentation.viewmodel", "get:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.example.sms_app.presentation.viewmodel.UpdateViewModel_HiltModules_KeyModule_ProvideFactory"]}